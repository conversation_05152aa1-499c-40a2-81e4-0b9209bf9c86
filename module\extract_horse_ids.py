#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
レース結果の表から馬IDを抽出し、その馬の情報をスクレイピングするスクリプト
"""

import os
import pandas as pd
import argparse  # main関数で使用

# import re # このファイル内では直接使用されていません
from tqdm import tqdm
from module.race_data_processor import RaceProcessor
from module.refactored_scrap import (
    scrape_html_horse,
    scrape_html_ped,
    logger,
    update_horse_master_file,
)  # update_horse_master_file をインポート
from module.constants import LocalPaths
from typing import List, Optional, Set  # 型ヒントのために追加
import pickle  # extract_horse_ids_from_race_data 関数内で使用
import glob  # extract_horse_ids_from_race_data 関数内で使用
from concurrent.futures import ThreadPoolExecutor, as_completed  # extract_horse_ids_from_race_data 関数内で使用


def extract_horse_ids_from_race_data(
    year: Optional[str] = None,
    race_id: Optional[str] = None,
    save_csv: bool = False,
    use_cache: bool = True,
    parallel: bool = True,
    max_workers: int = 4,
) -> List[str]:
    """
    レース結果から馬IDを抽出する（高速化版）

    Args:
        year (Optional[str], optional): 処理する年
        race_id (Optional[str], optional): 処理する特定のレースID
        save_csv (bool, optional): 抽出した馬IDをCSVとして保存するかどうか
        use_cache (bool, optional): キャッシュを使用するかどうか
        parallel (bool, optional): 並列処理を使用するかどうか
        max_workers (int, optional): 並列処理の最大ワーカー数

    Returns:
        list: 抽出した馬IDのリスト
    """
    logger.info(f"レースデータから馬IDを抽出します...")

    # キャッシュファイルのパスを決定
    cache_dir = os.path.join(LocalPaths.DATA_DIR, "cache")
    os.makedirs(cache_dir, exist_ok=True)

    cache_key_parts = ["horse_ids"]
    if year:
        cache_key_parts.append(str(year))
    if race_id:
        cache_key_parts.append(str(race_id))
    cache_key = "_".join(cache_key_parts)
    cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

    # キャッシュが存在し、使用する場合はキャッシュから読み込む
    if use_cache and os.path.exists(cache_file):
        logger.info(f"キャッシュからデータを読み込みます: {cache_file}")
        try:
            with open(cache_file, "rb") as f:
                horse_ids: List[str] = pickle.load(f)
                logger.info(f"キャッシュから読み込んだ馬ID数: {len(horse_ids)}")
                return horse_ids
        except Exception as e:
            logger.warning(f"キャッシュの読み込みに失敗しました: {e}。通常の処理を続行します。")
            # キャッシュの読み込みに失敗した場合は、通常の処理を続行

    # 処理対象のファイルパスを決定
    if race_id:
        # レースIDが指定された場合は、そのレースのファイルのみを処理
        year_from_id = race_id[:4]
        file_pattern = os.path.join(LocalPaths.HTML_RACE_DIR, year_from_id, f"{race_id}.bin") # "race_by_year" は LocalPaths.HTML_RACE_DIR に含まれる想定
        bin_files = glob.glob(file_pattern)
    elif year:
        # 年が指定された場合は、その年のファイルをすべて処理
        file_pattern = os.path.join(LocalPaths.HTML_RACE_DIR, str(year), "*.bin") # "race_by_year" は LocalPaths.HTML_RACE_DIR に含まれる想定
        bin_files = glob.glob(file_pattern)
    else:
        # 何も指定されていない場合は、すべてのファイルを処理
        file_pattern = os.path.join(LocalPaths.HTML_RACE_DIR, "*", "*.bin") # "race_by_year" は LocalPaths.HTML_RACE_DIR に含まれる想定
        bin_files = glob.glob(file_pattern)

    # ファイルが見つからない場合
    if not bin_files:
        logger.warning(f"処理対象のファイルが見つかりません: {file_pattern}")
        return []

    logger.info(f"処理対象のファイル数: {len(bin_files)}")

    # 単一ファイルから馬IDを抽出する内部関数
    def _extract_ids_from_single_file(file_path: str) -> List[str]:
        try:
            # 先頭バイトでHTMLか判定
            with open(file_path, 'rb') as f:
                head = f.read(1)
            if head != b'<':
                logger.warning(f"スキップ: {file_path} はHTMLファイルではありません（先頭バイト: {head}）")
                return []
            race_processor = RaceProcessor()
            horse_ids = race_processor.extract_horse_ids_from_html(file_path)
            return horse_ids
        except Exception as e:
            logger.error(f"ファイル {file_path} の処理中にエラーが発生しました: {e}", exc_info=True)
            return []

    all_horse_ids: Set[str] = set()

    if parallel and len(bin_files) > 1:
        # 並列処理の実行
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(_extract_ids_from_single_file, file_path): file_path for file_path in bin_files}

            for future in tqdm(as_completed(futures), total=len(bin_files), desc="馬ID抽出 (並列)"):
                file_path_completed = futures[future]
                try:
                    horse_ids_from_file = future.result()
                    all_horse_ids.update(horse_ids_from_file)
                except Exception as e:
                    logger.error(
                        f"ファイル {file_path_completed} の処理結果の取得中にエラーが発生しました: {e}", exc_info=True
                    )
    else:
        # 通常の処理（並列処理を使用しない場合、またはファイルが1つの場合）
        desc_text = "馬ID抽出 (非並列)" if len(bin_files) > 1 else "馬ID抽出"
        for file_path in tqdm(bin_files, desc=desc_text):
            try:
                horse_ids_from_file = _extract_ids_from_single_file(file_path)
                all_horse_ids.update(horse_ids_from_file)
            except Exception as e:
                logger.error(f"ファイル {file_path} の処理中にエラーが発生しました: {e}", exc_info=True)

    # セットをソート済みリストに変換
    horse_ids = sorted(list(all_horse_ids))

    if not horse_ids:
        logger.info("処理対象のファイルから馬IDが見つかりませんでした。")
        return []

    logger.info(f"抽出された馬ID数: {len(horse_ids)}")

    # キャッシュに保存
    try:
        with open(cache_file, "wb") as f:
            pickle.dump(horse_ids, f)
        logger.info(f"馬IDをキャッシュに保存しました: {cache_file}")
    except Exception as e:
        logger.error(f"キャッシュの保存に失敗しました: {e}", exc_info=True)

    # CSVとして保存
    if save_csv and horse_ids:
        # 保存先ディレクトリを作成
        csv_dir = os.path.join(LocalPaths.DATA_DIR, "csv")
        os.makedirs(csv_dir, exist_ok=True)

        # ファイル名を設定
        suffix_parts = []
        if year:
            suffix_parts.append(str(year))
        if race_id:
            suffix_parts.append(str(race_id))
        suffix = f"_{'_'.join(suffix_parts)}" if suffix_parts else ""

        horse_ids_csv = os.path.join(csv_dir, f"extracted_horse_ids{suffix}.csv")

        # DataFrameに変換して保存
        pd.DataFrame({"horse_id": horse_ids}).to_csv(horse_ids_csv, index=False)
        logger.info(f"馬IDをCSVとして保存しました: {horse_ids_csv}")

    return horse_ids


def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description="レース結果から馬IDを抽出し、馬情報をスクレイピングする")
    parser.add_argument("--year", type=str, help="処理する年")
    parser.add_argument("--race-id", type=str, help="処理する特定のレースID")
    parser.add_argument("--save-csv", action="store_true", help="抽出した馬IDをCSVとして保存する")
    parser.add_argument(
        "--scrape", choices=["horse", "ped", "all", "none"], default="none", help="スクレイピングするデータの種類"
    )
    parser.add_argument("--no-skip", action="store_true", help="既存のファイルをスキップしない")
    parser.add_argument("--horse-ids-file", type=str, help="馬IDが記載されたCSVファイル (horse_idカラムが必要)")
    parser.add_argument("--no-cache", action="store_true", help="キャッシュを使用しない")
    parser.add_argument("--no-parallel", action="store_true", help="並列処理を使用しない")
    parser.add_argument("--workers", type=int, default=4, help="並列処理の最大ワーカー数")
    parser.add_argument("--update-master", action="store_true", help="抽出/指定された馬IDでマスターファイルを更新する")
    parser.add_argument(
        "--scrape-new-only", action="store_true", help="マスターファイルに存在しない馬IDのみをスクレイピング対象とする"
    )

    args = parser.parse_args()

    # 馬IDの取得方法を決定
    horse_ids: List[str] = []

    if args.horse_ids_file:
        # CSVファイルから馬IDを読み込む
        try:
            horse_ids_df = pd.read_csv(args.horse_ids_file)
            if "horse_id" in horse_ids_df.columns:
                horse_ids = horse_ids_df["horse_id"].dropna().astype(str).unique().tolist()
                logger.info(f"CSVファイル '{args.horse_ids_file}' から馬IDを {len(horse_ids)}件読み込みました。")
            else:
                logger.error(f"CSVファイル '{args.horse_ids_file}' に 'horse_id' 列が見つかりません。")
        except Exception as e:
            logger.error(f"CSVファイル '{args.horse_ids_file}' の読み込み中にエラーが発生しました: {e}", exc_info=True)
    else:
        # レースデータから馬IDを抽出
        horse_ids = extract_horse_ids_from_race_data(
            year=args.year,
            race_id=args.race_id,
            save_csv=args.save_csv,
            use_cache=not args.no_cache,
            parallel=not args.no_parallel,
            max_workers=args.workers,
        )

    if not horse_ids:
        logger.info("処理対象の馬IDがありません。")
        return

    # スクレイピング対象の馬IDリスト (実際にスクレイピングを行うID)
    horse_ids_to_scrape: List[str] = list(horse_ids)  # コピーを作成

    # マスターファイルに存在しない馬IDのみをスクレイピングする場合
    if args.scrape_new_only:
        logger.info("マスターファイルに存在しない馬IDのみをスクレイピング対象とします。")
        try:
            if os.path.exists(LocalPaths.MASTER_RAW_HORSE_RESULTS_PATH):
                master_df = pd.read_csv(LocalPaths.MASTER_RAW_HORSE_RESULTS_PATH, dtype={"horse_id": str})
                existing_horse_ids = set(master_df["horse_id"].dropna().unique())
                horse_ids_to_scrape = [hid for hid in horse_ids_to_scrape if str(hid) not in existing_horse_ids]
                logger.info(f"新規スクレイピング対象の馬ID数: {len(horse_ids_to_scrape)}")
            else:
                logger.info("マスターファイルが存在しないため、全ての抽出/指定馬IDをスクレイピング対象とします。")
        except Exception as e:
            logger.error(f"マスターファイルの読み込みまたはフィルタリング中にエラーが発生しました: {e}", exc_info=True)
            logger.warning("フィルタリングに失敗したため、全ての抽出/指定馬IDをスクレイピング対象とします。")

    # スクレイピングを実行
    if args.scrape != "none":
        if horse_ids_to_scrape:
            logger.info(
                f"スクレイピングを開始します: type='{args.scrape}', skip={not args.no_skip}, 対象馬ID数={len(horse_ids_to_scrape)}"
            )
            if args.scrape == "horse" or args.scrape == "all":
                logger.info(f"馬情報 ({len(horse_ids_to_scrape)}件) をスクレイピングします...")
                scrape_html_horse(horse_id_list=horse_ids_to_scrape, skip=not args.no_skip)
            if args.scrape == "ped" or args.scrape == "all":
                logger.info(f"血統情報 ({len(horse_ids_to_scrape)}件) をスクレイピングします...")
                scrape_html_ped(horse_id_list=horse_ids_to_scrape, skip=not args.no_skip)
            logger.info("スクレイピングが完了しました。")
        else:
            logger.info("スクレイピング対象の馬IDがありません。")
    else:  # args.scrape == 'none'
        logger.info("スクレイピングは指定されていません。")

    # マスターファイルを更新
    if args.update_master and horse_ids:  # 元のhorse_ids (フィルタリング前) を使って更新
        logger.info(f"馬情報マスターファイルを更新します。対象馬ID数: {len(horse_ids)}")
        update_horse_master_file(horse_ids_to_update=horse_ids)


if __name__ == "__main__":
    main()
