import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from pathlib import Path
import pickle
from typing import List, Set, Dict, Optional, Union
from datetime import datetime
import glob
from tqdm.auto import tqdm


class RaceFirstProcessor:
    def __init__(self, base_dir: str = "data"):
        self.base_dir = Path(base_dir)
        self.logger = logging.getLogger(__name__)
        # ログレベルをDEBUGに設定
        self.logger.setLevel(logging.DEBUG)

    def _find_latest_race_file(self, year: str) -> Optional[Path]:
        """指定年のレースデータファイルの中で最新のものを探す

        Args:
            year (str): 対象年度

        Returns:
            Optional[Path]: 最新のファイルパス。見つからない場合はNone
        """
        # race_data_processor.py の save_race_data_to_csv のファイル名形式に合わせる
        pattern = str(self.base_dir / f"race_data_results_{year}_*.pickle") # "results" を追加
        files = sorted(glob.glob(pattern), reverse=True)
        if not files:
            self.logger.error(f"レースデータファイルが見つかりません。パターン: {pattern}")
            return None
        self.logger.debug(f"見つかったファイル: {files[0]}")
        return Path(files[0])

    def process_races_first(self, target_years: List[str], max_workers: int = 4) -> tuple[pd.DataFrame, Set[str]]:
        """レース情報を先に処理し、馬IDを抽出する

        Args:
            target_years (List[str]): 処理対象年度のリスト
            max_workers (int): 並列処理のワーカー数

        Returns:
            tuple[pd.DataFrame, Set[str]]: (レース結果DataFrame, 馬IDのセット)
        """
        # レース結果の保存先を作成
        race_data_dir = self.base_dir / "processed"
        race_data_dir.mkdir(exist_ok=True)

        all_race_results = []
        all_horse_ids = set()
        processed_races_count = 0

        for year in tqdm(target_years, desc="年度別レース処理"):
            self.logger.info(f"{year}年のレース処理を開始")

            # 最新のレースデータファイルを探す
            race_file = self._find_latest_race_file(year)
            if race_file is None:
                self.logger.warning(f"{year}年のレースデータファイルが見つかりません")
                continue

            self.logger.info(f"レースファイルを読み込み中: {race_file}")
            try:
                with open(race_file, "rb") as f:
                    year_races = pickle.load(f)

                self.logger.debug(f"読み込んだデータの型: {type(year_races)}")
                self.logger.debug(f"データサイズ: {len(year_races) if year_races is not None else 'None'}")

                # DataFrameの場合の処理
                if isinstance(year_races, pd.DataFrame):
                    self.logger.info(f"{len(year_races)}行のDataFrameを処理中")
                    # レースIDごとにグループ化して処理
                    for race_id, race_group in tqdm(year_races.groupby("race_id"), desc=f"{year}年レース処理(DataFrame)", leave=False):
                        try:
                            race_data = {
                                "result_table": race_group.to_dict("records"),
                                "race_date": (
                                    race_group["race_date"].iloc[0] if "race_date" in race_group.columns else ""
                                ),
                                "race_name": (
                                    race_group["race_name"].iloc[0] if "race_name" in race_group.columns else ""
                                ),
                                "race_course": (
                                    race_group["race_course"].iloc[0] if "race_course" in race_group.columns else ""
                                ),
                                "weather": race_group["weather"].iloc[0] if "weather" in race_group.columns else "",
                                "ground_state": (
                                    race_group["ground_state"].iloc[0] if "ground_state" in race_group.columns else ""
                                ),
                                "race_type": (
                                    race_group["race_type"].iloc[0] if "race_type" in race_group.columns else ""
                                ),
                            }
                            race_result, horse_ids = self._process_single_race(race_id, race_data)
                            if race_result is not None:
                                all_race_results.append(race_result)
                                all_horse_ids.update(horse_ids)
                                processed_races_count += 1
                        except Exception as e:
                            self.logger.error(f"レース{race_id}の処理中にエラー: {str(e)}", exc_info=True)

                # 辞書の場合の処理
                elif isinstance(year_races, dict):
                    self.logger.info(f"{len(year_races)}件のレースを処理中")
                    with ThreadPoolExecutor(max_workers=max_workers) as executor:
                        future_to_race = {
                            executor.submit(self._process_single_race, race_id, race_data): race_id
                            for race_id, race_data in year_races.items()
                        }

                        for future in tqdm(as_completed(future_to_race), total=len(future_to_race), desc=f"{year}年レース処理(並列)", leave=False):
                            race_id = future_to_race[future]
                            try:
                                race_result, horse_ids = future.result()
                                if race_result is not None:
                                    all_race_results.append(race_result)
                                    all_horse_ids.update(horse_ids)
                                    processed_races_count += 1
                            except Exception as e:
                                self.logger.error(f"レース{race_id}の処理中にエラー: {str(e)}", exc_info=True)
                else:
                    self.logger.error(f"未対応のデータ型です: {type(year_races)}")
                    continue

            except Exception as e:
                self.logger.error(f"レースファイル{race_file}の読み込み中にエラー: {str(e)}", exc_info=True)
                continue

        # 全レース結果を結合
        if not all_race_results:
            self.logger.error(f"正常に処理されたレースがありません。処理試行数: {processed_races_count}")
            raise ValueError("正常に処理されたレースがありません")

        self.logger.info(f"レース結果を結合中... 処理成功数: {len(all_race_results)}")
        combined_results = pd.concat(all_race_results, ignore_index=True)

        # 処理済みデータを保存
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = race_data_dir / f"processed_races_{timestamp}.pickle"
        with open(output_path, "wb") as f:
            pickle.dump({"race_results": combined_results, "horse_ids": list(all_horse_ids)}, f)

        self.logger.info(f"{len(combined_results)}件のレース結果と{len(all_horse_ids)}頭の馬を処理しました")
        return combined_results, all_horse_ids

    def _process_single_race(self, race_id: str, race_data: Dict) -> tuple[Optional[pd.DataFrame], Set[str]]:
        """単一のレース情報を処理する

        Args:
            race_id (str): レースID
            race_data (Dict): レース情報の辞書

        Returns:
            tuple[Optional[pd.DataFrame], Set[str]]: (レース結果DataFrame, 馬IDのセット)
        """
        try:
            # レース結果テーブルを取得
            result_table = race_data.get("result_table", [])
            if not result_table:
                self.logger.warning(f"レース{race_id}の結果テーブルが空です")
                return None, set()

            # DataFrameに変換
            if isinstance(result_table, list):
                result_df = pd.DataFrame(result_table)
            else:
                result_df = pd.DataFrame([result_table])

            if result_df.empty:
                self.logger.warning(f"レース{race_id}のDataFrameが空です")
                return None, set()

            # データの内容をログ出力
            self.logger.debug(f"レース{race_id}のカラム: {result_df.columns.tolist()}")
            self.logger.debug(f"レース{race_id}のデータ形状: {result_df.shape}")

            # 基本情報を追加
            result_df["race_id"] = race_id
            for col, value in {
                "race_date": race_data.get("race_date", ""),
                "race_name": race_data.get("race_name", ""),
                "race_course": race_data.get("race_course", ""),
                "weather": race_data.get("weather", ""),
                "ground_state": race_data.get("ground_state", ""),
                "race_type": race_data.get("race_type", ""),
            }.items():
                result_df[col] = value

            # 馬IDを抽出
            if "horse_id" not in result_df.columns:
                self.logger.error(f"レース{race_id}に馬IDカラムがありません")
                return None, set()

            horse_ids = set(result_df["horse_id"].dropna().unique())
            if not horse_ids:
                self.logger.warning(f"レース{race_id}から有効な馬IDが抽出できませんでした")
                return None, set()

            self.logger.debug(f"レース{race_id}を正常に処理: {len(horse_ids)}頭の馬")
            return result_df, horse_ids

        except Exception as e:
            self.logger.error(f"レース{race_id}の処理中にエラー: {str(e)}", exc_info=True)
            return None, set()
