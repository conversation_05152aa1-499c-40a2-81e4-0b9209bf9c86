#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTMLファイルの構造を調査して性齢情報の有無を確認するスクリプト
"""

import sys
import os
from pathlib import Path
import pandas as pd
from bs4 import BeautifulSoup

# moduleディレクトリをパスに追加
sys.path.append('module')

def investigate_html_structure():
    """HTMLファイルの構造を詳細に調査"""
    print("=== HTMLファイル構造調査 ===")

    # 馬の過去成績ファイルを調査
    print("\n--- 馬の過去成績ファイル調査 ---")
    horse_file = Path("data/html/horse/horse_by_year/2022/2022100006.bin")
    investigate_single_file(horse_file, "馬の過去成績")

    # レース結果ファイルを調査
    print("\n--- レース結果ファイル調査 ---")
    race_file = Path("data/html/race/race_by_year/2022/202201010101.bin")
    investigate_single_file(race_file, "レース結果")

def investigate_single_file(test_file, file_type):
    """単一のHTMLファイルを調査"""

    if not test_file.exists():
        print(f"テストファイルが存在しません: {test_file}")
        return

    print(f"調査対象ファイル ({file_type}): {test_file}")

    try:
        # HTMLファイルを読み込み
        with open(test_file, 'rb') as f:
            html_content = f.read()

        # BeautifulSoupでパース
        soup = BeautifulSoup(html_content, "lxml", from_encoding="euc-jp")

        # すべてのテーブルを取得
        tables = soup.find_all('table')
        print(f"テーブル数: {len(tables)}")

        # pandasでテーブルを読み込み
        df_tables = pd.read_html(str(soup))
        print(f"pandas読み込みテーブル数: {len(df_tables)}")

        # 各テーブルの列名を確認
        for i, table in enumerate(df_tables):
            print(f"\n--- テーブル {i+1} ---")
            print(f"形状: {table.shape}")
            print(f"列名: {list(table.columns)}")

            # 性齢に関連する列があるかチェック
            sex_age_related = [col for col in table.columns if any(keyword in str(col) for keyword in ['性', '齢', 'sex', 'age', '牡', '牝', 'セ'])]
            if sex_age_related:
                print(f"性齢関連列: {sex_age_related}")

            # 最初の数行を表示
            if not table.empty:
                print("最初の3行:")
                print(table.head(3))

        # HTMLの生テキストから性齢情報を検索
        print("\n=== HTML生テキスト検索 ===")
        html_text = soup.get_text()

        # 性齢パターンを検索
        import re
        sex_age_patterns = [
            r'[牡牝セ][0-9]+',  # 牡3, 牝4, セ5 など
            r'性齢',
            r'性別',
            r'年齢'
        ]

        for pattern in sex_age_patterns:
            matches = re.findall(pattern, html_text)
            if matches:
                print(f"パターン '{pattern}' の一致: {matches[:10]}")  # 最初の10個

        # テーブルのHTMLを詳細に調査
        print("\n=== テーブルHTML詳細調査 ===")
        for i, table_element in enumerate(tables):
            print(f"\nテーブル {i+1} のHTML構造:")

            # ヘッダー行を探す
            headers = table_element.find_all(['th', 'td'])
            header_texts = [header.get_text(strip=True) for header in headers[:20]]  # 最初の20個
            print(f"ヘッダー候補: {header_texts}")

            # 性齢関連のテキストがあるかチェック
            sex_age_in_headers = [text for text in header_texts if any(keyword in text for keyword in ['性', '齢', '牡', '牝', 'セ'])]
            if sex_age_in_headers:
                print(f"性齢関連ヘッダー: {sex_age_in_headers}")

    except Exception as e:
        print(f"エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigate_html_structure()
