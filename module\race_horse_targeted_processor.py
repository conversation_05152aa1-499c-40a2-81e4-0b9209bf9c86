#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
レースデータから抽出した馬IDのみをHorseProcessorで処理するスクリプト
効率的に必要な馬データのみを処理する
"""

import logging
import os
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Optional, Set, Dict, Any
from tqdm.auto import tqdm

from module.race_data_processor import RaceProcessor
from module.horse_processor import HorseProcessor
from module.extract_horse_ids import extract_horse_ids_from_race_data


class RaceHorseTargetedProcessor:
    """
    レースデータから抽出した馬IDのみを対象にした馬情報処理クラス
    """
    
    def __init__(self):
        """初期化"""
        self.logger = logging.getLogger(__name__)
        self.race_processor = RaceProcessor()
        self.horse_processor = HorseProcessor()
        
    def extract_horse_ids_from_race_results(self, 
                                          year: Optional[str] = None,
                                          race_id: Optional[str] = None,
                                          parallel: bool = True,
                                          max_workers: int = 4) -> Set[str]:
        """
        レース結果データから馬IDを抽出
        
        Parameters
        ----------
        year : str, optional
            処理する年度
        race_id : str, optional
            特定のレースID
        parallel : bool, default True
            並列処理を使用するか
        max_workers : int, default 4
            並列処理の最大ワーカー数
            
        Returns
        -------
        Set[str]
            ユニークな馬IDのセット
        """
        self.logger.info("レース結果から馬IDを抽出しています...")
        
        # extract_horse_ids.pyの関数を使用
        horse_ids_list = extract_horse_ids_from_race_data(
            year=year,
            race_id=race_id,
            save_csv=False,
            use_cache=True,
            parallel=parallel,
            max_workers=max_workers
        )
        
        # リストをセットに変換してユニークにする
        unique_horse_ids = set(horse_ids_list)
        
        self.logger.info(f"抽出完了: {len(unique_horse_ids)}頭の馬ID")
        return unique_horse_ids
    
    def extract_horse_ids_from_dataframe(self, race_results_df: pd.DataFrame) -> Set[str]:
        """
        レース結果DataFrameから馬IDを抽出
        
        Parameters
        ----------
        race_results_df : pd.DataFrame
            レース結果のDataFrame
            
        Returns
        -------
        Set[str]
            ユニークな馬IDのセット
        """
        if race_results_df.empty:
            self.logger.warning("レース結果DataFrameが空です")
            return set()
        
        # horse_idカラムから馬IDを抽出
        if 'horse_id' in race_results_df.columns:
            horse_ids = race_results_df['horse_id'].dropna().astype(str)
            unique_horse_ids = set(horse_ids.tolist())
            
            # 空文字を除外
            unique_horse_ids.discard('')
            unique_horse_ids.discard('nan')
            
            self.logger.info(f"DataFrameから{len(unique_horse_ids)}頭の馬IDを抽出")
            return unique_horse_ids
        else:
            self.logger.error("horse_idカラムが見つかりません")
            return set()
    
    def process_targeted_horses(self, 
                              horse_ids: Set[str],
                              include_basic_info: bool = True,
                              include_results: bool = True,
                              parallel: bool = True,
                              max_workers: int = 4) -> Dict[str, pd.DataFrame]:
        """
        指定された馬IDのみを対象に馬情報を処理
        
        Parameters
        ----------
        horse_ids : Set[str]
            処理対象の馬IDセット
        include_basic_info : bool, default True
            馬基本情報を含めるか
        include_results : bool, default True
            馬過去成績を含めるか
        parallel : bool, default True
            並列処理を使用するか
        max_workers : int, default 4
            並列処理の最大ワーカー数
            
        Returns
        -------
        Dict[str, pd.DataFrame]
            処理結果のDataFrame辞書
            キー: 'horse_info', 'horse_results'
        """
        if not horse_ids:
            self.logger.warning("処理対象の馬IDが空です")
            return {'horse_info': pd.DataFrame(), 'horse_results': pd.DataFrame()}
        
        self.logger.info(f"{len(horse_ids)}頭の馬情報を処理開始")
        
        result = {'horse_info': pd.DataFrame(), 'horse_results': pd.DataFrame()}
        
        # 並列処理のためのExecutorを設定
        # ここでのmax_workersは、基本情報処理と過去成績処理の2タスクを同時に実行するため、
        # 2程度で十分かもしれませんが、HorseProcessor内部の並列度も考慮して調整が必要です。
        # ここでは引数のmax_workersをそのまま利用しますが、最大2つのタスクしかないので、
        # 実際の並列度は最大2になります。
        actual_max_workers_for_this_level = min(max_workers, 2) if parallel else 1

        with ThreadPoolExecutor(max_workers=actual_max_workers_for_this_level) as executor:
            futures = {}
            if include_basic_info:
                self.logger.info("馬基本情報処理をサブミット...")
                future_info = executor.submit(
                    self.horse_processor.process_horse_info_for_ids,
                    horse_ids=list(horse_ids),
                    parallel=parallel,      # HorseProcessor内の並列処理フラグ
                    max_workers=max_workers # HorseProcessor内のワーカー数
                )
                futures[future_info] = 'horse_info'

            if include_results:
                self.logger.info("馬過去成績処理をサブミット...")
                future_results = executor.submit(
                    self.horse_processor.process_horse_results_for_ids, # メソッド名を修正
                    horse_ids=list(horse_ids), # HorseProcessor側でリストを期待
                    parallel=parallel,         # HorseProcessor内部の並列化フラグ
                    max_workers=max_workers    # HorseProcessor内部のワーカー数
                )
                futures[future_results] = 'horse_results'

            for future in tqdm(as_completed(futures), total=len(futures), desc="対象馬情報処理(並列)"):
                task_name = futures[future]
                try:
                    data_df = future.result()
                    result[task_name] = data_df
                    self.logger.info(f"{task_name} 処理完了: {len(data_df)}件")
                except Exception as e:
                    self.logger.error(f"{task_name} の処理中にエラーが発生: {e}", exc_info=True)
                    # エラーが発生した場合でも、対応するキーには空のDataFrameを維持
                    result[task_name] = pd.DataFrame()
                
        return result
    
    def process_race_to_horses(self,
                             year: Optional[str] = None,
                             race_id: Optional[str] = None,
                             include_basic_info: bool = True,
                             include_results: bool = True,
                             parallel: bool = True,
                             max_workers: int = 4,
                             save_output: bool = False,
                             output_prefix: str = "race_horses") -> Dict[str, pd.DataFrame]:
        """
        レースデータから馬IDを抽出し、対象馬の情報を処理する一連の流れ
        
        Parameters
        ----------
        year : str, optional
            処理する年度
        race_id : str, optional
            特定のレースID
        include_basic_info : bool, default True
            馬基本情報を含めるか
        include_results : bool, default True
            馬過去成績を含めるか
        parallel : bool, default True
            並列処理を使用するか
        max_workers : int, default 4
            並列処理の最大ワーカー数
        save_output : bool, default False
            結果をファイルに保存するか
        output_prefix : str, default "race_horses"
            出力ファイルのプレフィックス
            
        Returns
        -------
        Dict[str, pd.DataFrame]
            処理結果のDataFrame辞書
        """
        self.logger.info("レース→馬情報の統合処理を開始")
        
        # Step 1: レースデータから馬IDを抽出
        horse_ids = self.extract_horse_ids_from_race_results(
            year=year,
            race_id=race_id,
            parallel=parallel,
            max_workers=max_workers
        )
        
        if not horse_ids:
            self.logger.warning("抽出された馬IDがありません")
            return {'horse_info': pd.DataFrame(), 'horse_results': pd.DataFrame()}
        
        # Step 2: 抽出した馬IDで馬情報を処理
        result = self.process_targeted_horses(
            horse_ids=horse_ids,
            include_basic_info=include_basic_info,
            include_results=include_results,
            parallel=parallel,
            max_workers=max_workers
        )
        
        # Step 3: 必要に応じて保存
        if save_output:
            self._save_results(result, output_prefix, year, race_id)
        
        return result
    
    def _save_results(self, 
                     result: Dict[str, pd.DataFrame], 
                     output_prefix: str,
                     year: Optional[str] = None,
                     race_id: Optional[str] = None):
        """結果をファイルに保存"""
        try:
            # 出力ディレクトリを作成
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            
            # ファイル名の生成
            suffix = ""
            if year:
                suffix += f"_{year}"
            if race_id:
                suffix += f"_{race_id}"
            
            # 各DataFrameを保存
            for key, df in result.items():
                if not df.empty:
                    # Pickleファイルとして保存
                    pickle_path = f"{output_dir}/{output_prefix}_{key}{suffix}.pickle"
                    df.to_pickle(pickle_path)
                    
                    # CSVファイルとしても保存
                    csv_path = f"{output_dir}/{output_prefix}_{key}{suffix}.csv"
                    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                    
                    self.logger.info(f"{key}を保存: {pickle_path}, {csv_path}")
                    
        except Exception as e:
            self.logger.error(f"結果の保存中にエラーが発生: {e}", exc_info=True)


def main():
    """メイン関数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='レースデータから抽出した馬IDで馬情報を処理')
    parser.add_argument('--year', type=str, help='処理する年度')
    parser.add_argument('--race-id', type=str, help='特定のレースID')
    parser.add_argument('--no-basic-info', action='store_true', help='馬基本情報を含めない')
    parser.add_argument('--no-results', action='store_true', help='馬過去成績を含めない')
    parser.add_argument('--no-parallel', action='store_true', help='並列処理を使用しない')
    parser.add_argument('--workers', type=int, default=4, help='並列処理の最大ワーカー数')
    parser.add_argument('--save', action='store_true', help='結果をファイルに保存')
    parser.add_argument('--output-prefix', type=str, default='race_horses', help='出力ファイルのプレフィックス')
    parser.add_argument('--debug', action='store_true', help='デバッグモードで実行')
    
    args = parser.parse_args()
    
    # ログ設定
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # プロセッサを作成
    processor = RaceHorseTargetedProcessor()
    
    # 処理実行
    result = processor.process_race_to_horses(
        year=args.year,
        race_id=args.race_id,
        include_basic_info=not args.no_basic_info,
        include_results=not args.no_results,
        parallel=not args.no_parallel,
        max_workers=args.workers,
        save_output=args.save,
        output_prefix=args.output_prefix
    )
    
    # 結果の概要を表示
    print("\n=== 処理結果 ===")
    for key, df in result.items():
        print(f"{key}: {len(df)}件")
        if not df.empty and len(df) > 0:
            print(f"  カラム数: {len(df.columns)}")
            print(f"  最初の数行のカラム: {list(df.columns[:5])}")


if __name__ == "__main__":
    main()