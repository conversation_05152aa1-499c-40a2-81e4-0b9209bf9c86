import os
import shutil
from pathlib import Path

# データディレクトリ
DATA_DIR = Path(__file__).parent / "data"
from tqdm.auto import tqdm

# サブフォルダ作成
def ensure_dir(path):
    if not path.exists():
        path.mkdir(parents=True)

def sort_race_files_by_year():
    race_dir = DATA_DIR / "html" / "race"
    race_by_year_dir = race_dir / "race_by_year"
    ensure_dir(race_by_year_dir)
    for file in race_dir.iterdir():
        if file.is_file():
            # 年をファイル名から抽出（例: race_data_2022_xxx.html → 2022）
            import re
            m = re.search(r'(20\d{2})', file.name)
            if m:
                year = m.group(1)
                dest_dir = race_by_year_dir / year
                ensure_dir(dest_dir)
                shutil.move(str(file), str(dest_dir / file.name))
                print(f"{file.name} → race_by_year/{year}/")
            else:
                # 年が抽出できない場合はrace_by_year/othersへ
                dest_dir = race_by_year_dir / "others"
                ensure_dir(dest_dir)
                shutil.move(str(file), str(dest_dir / file.name))
                print(f"{file.name} → race_by_year/others/")

def sort_horse_files_by_year():
    horse_dir = DATA_DIR / "html" / "horse"
    horse_by_year_dir = horse_dir / "horse_by_year"
    ensure_dir(horse_by_year_dir)
    for file in horse_dir.iterdir():
        if file.is_file():
            import re
            m = re.search(r'(20\d{2})', file.name)
            if m:
                year = m.group(1)
                dest_dir = horse_by_year_dir / year
                ensure_dir(dest_dir)
                shutil.move(str(file), str(dest_dir / file.name))
                print(f"{file.name} → horse_by_year/{year}/")
            else:
                dest_dir = horse_by_year_dir / "others"
                ensure_dir(dest_dir)
                shutil.move(str(file), str(dest_dir / file.name))
                print(f"{file.name} → horse_by_year/others/")

def sort_horse_files_by_year_fast():
    import re
    horse_dir = DATA_DIR / "html" / "horse"
    horse_by_year_dir = horse_dir / "horse_by_year"
    ensure_dir(horse_by_year_dir)
    # まとめてファイルリストを取得
    files = [f for f in horse_dir.iterdir() if f.is_file()]
    # 年ごとにグループ化
    year_map = {}
    for file in files:
        m = re.search(r'(20\d{2})', file.name)
        year = m.group(1) if m else "others"
        year_map.setdefault(year, []).append(file)
    # 年ごとに一括移動
    for year, file_list in tqdm(year_map.items(), desc="馬HTMLファイル仕分け(年毎)"):
        dest_dir = horse_by_year_dir / year
        ensure_dir(dest_dir)
        for file in tqdm(file_list, desc=f"  {year}年 移動中", leave=False):
            shutil.move(str(file), str(dest_dir / file.name))
        print(f"{len(file_list)} files → horse_by_year/{year}/")

def sort_race_files_by_year_fast():
    import re
    race_dir = DATA_DIR / "html" / "race"
    race_by_year_dir = race_dir / "race_by_year"
    ensure_dir(race_by_year_dir)
    files = [f for f in race_dir.iterdir() if f.is_file()]
    year_map = {}
    for file in files:
        m = re.search(r'(20\d{2})', file.name)
        year = m.group(1) if m else "others"
        year_map.setdefault(year, []).append(file)
    for year, file_list in tqdm(year_map.items(), desc="レースHTMLファイル仕分け(年毎)"):
        dest_dir = race_by_year_dir / year
        ensure_dir(dest_dir)
        for file in tqdm(file_list, desc=f"  {year}年 移動中", leave=False):
            shutil.move(str(file), str(dest_dir / file.name))
        print(f"{len(file_list)} files → race_by_year/{year}/")

def sort_pedigree_files_by_year_fast():
    import re
    pedigree_dir = DATA_DIR / "html" / "pedigree"
    pedigree_by_year_dir = pedigree_dir / "pedigree_by_year"
    ensure_dir(pedigree_by_year_dir)
    files = [f for f in pedigree_dir.iterdir() if f.is_file()]
    year_map = {}
    for file in files:
        m = re.search(r'(20\d{2})', file.name)
        year = m.group(1) if m else "others"
        year_map.setdefault(year, []).append(file)
    for year, file_list in tqdm(year_map.items(), desc="血統HTMLファイル仕分け(年毎)"):
        dest_dir = pedigree_by_year_dir / year
        ensure_dir(dest_dir)
        for file in tqdm(file_list, desc=f"  {year}年 移動中", leave=False):
            shutil.move(str(file), str(dest_dir / file.name))
        print(f"{len(file_list)} files → pedigree_by_year/{year}/")

if __name__ == "__main__":
    sort_race_files_by_year_fast()
    sort_horse_files_by_year_fast()
    sort_pedigree_files_by_year_fast()
    print("仕分け完了")
