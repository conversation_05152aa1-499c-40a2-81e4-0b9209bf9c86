{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# レースデータから抽出した馬IDで馬情報処理の例\n", "\n", "このノートブックでは、レースデータから抽出した馬IDのみを対象にして、効率的に馬の基本情報と過去成績を処理する方法を示します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import pandas as pd\n", "from race_horse_targeted_processor import RaceHorseTargetedProcessor\n", "from module.race_data_processor import RaceProcessor\n", "\n", "# ログ設定\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. プロセッサの初期化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# レース→馬情報のターゲット処理プロセッサを作成\n", "processor = RaceHorseTargetedProcessor()\n", "\n", "# レース処理プロセッサも作成（レースデータ直接処理用）\n", "race_processor = RaceProcessor()\n", "\n", "print(\"プロセッサの初期化完了\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 方法1: 年度を指定してレース→馬情報を一括処理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2024年のレースから馬IDを抽出し、馬情報を処理\n", "result_2024 = processor.process_race_to_horses(\n", "    year=\"2024\",\n", "    include_basic_info=True,\n", "    include_results=True,\n", "    parallel=True,\n", "    max_workers=4,\n", "    save_output=True,\n", "    output_prefix=\"race_horses_2024\"\n", ")\n", "\n", "print(\"\\n=== 2024年レース→馬情報処理結果 ===\")\n", "for key, df in result_2024.items():\n", "    print(f\"{key}: {len(df)}件\")\n", "    if not df.empty:\n", "        print(f\"  カラム例: {list(df.columns[:5])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 方法2: 特定のレースIDを指定して処理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特定のレースIDを指定（例）\n", "specific_race_id = \"202406010101\"  # 実際のレースIDに置き換えてください\n", "\n", "result_specific = processor.process_race_to_horses(\n", "    race_id=specific_race_id,\n", "    include_basic_info=True,\n", "    include_results=True,\n", "    parallel=True,\n", "    max_workers=2\n", ")\n", "\n", "print(f\"\\n=== レースID {specific_race_id} の処理結果 ===\")\n", "for key, df in result_specific.items():\n", "    print(f\"{key}: {len(df)}件\")\n", "    if not df.empty:\n", "        print(f\"  サンプルデータ:\")\n", "        print(df.head(2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 方法3: 既存のレース結果DataFrameから馬IDを抽出して処理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# まず、いくつかのレース結果を取得（例として2024年の一部）\n", "import glob\n", "\n", "# レースHTMLファイルを取得\n", "race_html_files = glob.glob(\"data/html/race/race_by_year/2024/*.bin\")[:5]  # 最初の5ファイル\n", "\n", "race_info_list = []\n", "race_results_list = []\n", "\n", "for bin_file in race_html_files:\n", "    try:\n", "        info_df, results_df = race_processor.parse_race_html(html_path=bin_file)\n", "        if not info_df.empty:\n", "            race_info_list.append(info_df)\n", "        if not results_df.empty:\n", "            race_results_list.append(results_df)\n", "    except Exception as e:\n", "        print(f\"エラー: {bin_file} - {e}\")\n", "\n", "# レース結果を結合\n", "if race_results_list:\n", "    combined_race_results = pd.concat(race_results_list, ignore_index=True)\n", "    print(f\"レース結果データ: {len(combined_race_results)}件\")\n", "    print(f\"カラム: {list(combined_race_results.columns)}\")\nelse:\n", "    combined_race_results = pd.DataFrame()\n", "    print(\"レース結果データがありません\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data<PERSON><PERSON>eから馬IDを抽出\n", "if not combined_race_results.empty:\n", "    horse_ids = processor.extract_horse_ids_from_dataframe(combined_race_results)\n", "    print(f\"抽出された馬ID数: {len(horse_ids)}\")\n", "    print(f\"馬IDの例: {list(horse_ids)[:10]}\")\n", "    \n", "    # 抽出した馬IDで馬情報を処理\n", "    targeted_result = processor.process_targeted_horses(\n", "        horse_ids=horse_ids,\n", "        include_basic_info=True,\n", "        include_results=True,\n", "        parallel=True,\n", "        max_workers=4\n", "    )\n", "    \n", "    print(\"\\n=== ターゲット馬情報処理結果 ===\")\n", "    for key, df in targeted_result.items():\n", "        print(f\"{key}: {len(df)}件\")\n", "        if not df.empty and len(df) > 0:\n", "            print(f\"  カラム数: {len(df.columns)}\")\n", "            print(f\"  最初の数行:\")\n", "            print(df.head(2))\nelse:\n", "    print(\"処理可能なレース結果データがありません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 結果の分析とサマリー"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 馬基本情報のサマリー\n", "if 'horse_info' in targeted_result and not targeted_result['horse_info'].empty:\n", "    horse_info_df = targeted_result['horse_info']\n", "    \n", "    print(\"=== 馬基本情報サマリー ===\")\n", "    print(f\"総馬数: {len(horse_info_df)}\")\n", "    \n", "    # 血統情報の有無をチェック\n", "    pedigree_cols = [col for col in horse_info_df.columns \n", "                    if any(keyword in col.lower() for keyword in ['father', 'mother'])]\n", "    if pedigree_cols:\n", "        print(f\"血統情報カラム: {pedigree_cols}\")\n", "        \n", "        # 血統情報の充足率\n", "        for col in pedigree_cols[:3]:  # 最初の3つのカラムで確認\n", "            non_null_count = horse_info_df[col].notna().sum()\n", "            rate = non_null_count / len(horse_info_df) * 100\n", "            print(f\"  {col}: {non_null_count}/{len(horse_info_df)} ({rate:.1f}%)\")\n", "\n", "# 馬過去成績のサマリー\n", "if 'horse_results' in targeted_result and not targeted_result['horse_results'].empty:\n", "    horse_results_df = targeted_result['horse_results']\n", "    \n", "    print(\"\\n=== 馬過去成績サマリー ===\")\n", "    print(f\"総レコード数: {len(horse_results_df)}\")\n", "    \n", "    if 'horse_id' in horse_results_df.columns:\n", "        unique_horses = horse_results_df['horse_id'].nunique()\n", "        avg_records_per_horse = len(horse_results_df) / unique_horses\n", "        print(f\"ユニーク馬数: {unique_horses}\")\n", "        print(f\"1頭あたり平均レコード数: {avg_records_per_horse:.1f}\")\n", "    \n", "    # 成績データの基本統計\n", "    numeric_cols = horse_results_df.select_dtypes(include=['number']).columns\n", "    if len(numeric_cols) > 0:\n", "        print(f\"\\n数値カラムの基本統計:\")\n", "        print(horse_results_df[numeric_cols].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. コマンドライン実行の例\n", "\n", "スクリプトをコマンドラインから実行する場合の例："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> Notebookからシェルコマンドを実行\n", "# 実際の実行時はコメントアウトを外してください\n", "\n", "# 2024年のデータを処理し、結果を保存\n", "# !python race_horse_targeted_processor.py --year 2024 --save --workers 4\n", "\n", "# 特定のレースIDを処理\n", "# !python race_horse_targeted_processor.py --race-id 202406010101 --save\n", "\n", "# 馬基本情報のみを処理（過去成績は除外）\n", "# !python race_horse_targeted_processor.py --year 2024 --no-results --save\n", "\n", "# デバッグモードで実行\n", "# !python race_horse_targeted_processor.py --year 2024 --debug --workers 2\n", "\n", "print(\"コマンドライン実行例（上記参照）\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## まとめ\n", "\n", "このノートブックでは以下の方法を示しました：\n", "\n", "1. **年度指定**: 特定年度のレースから馬IDを抽出して馬情報を処理\n", "2. **レースID指定**: 特定のレースから馬IDを抽出して処理\n", "3. **DataFrame指定**: 既存のレース結果DataFrameから馬IDを抽出して処理\n", "\n", "### 利点\n", "- **効率性**: 必要な馬のデータのみを処理\n", "- **柔軟性**: 様々なデータソースに対応\n", "- **並列処理**: 高速な処理が可能\n", "- **自動保存**: 結果を自動的にファイル保存可能\n", "\n", "### 応用例\n", "- 特定のG1レースの出走馬分析\n", "- 月別・年度別の馬データ分析\n", "- 機械学習用の特徴量生成\n", "- レース予測モデルの学習データ作成"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}