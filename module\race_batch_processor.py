"""
レースデータのバッチ処理用関数群
"""

import os
import glob
import pickle
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple, Set
from concurrent.futures import ThreadPoolExecutor, as_completed

import pandas as pd
from tqdm import tqdm

# 既存のプロセッサをインポート
from module.race_data_processor import RaceProcessor


def find_latest_race_file(base_dir: str, year: str) -> Optional[Path]:
    """指定年のレースデータファイルの中で最新のものを探す

    Args:
        base_dir (str): データディレクトリのベースパス
        year (str): 対象年度

    Returns:
        Optional[Path]: 最新のファイルパス。見つからない場合はNone
    """
    base_path = Path(base_dir)
    pattern = str(base_path / f"race_data_results_{year}_*.pickle")
    files = sorted(glob.glob(pattern), reverse=True)

    if not files:
        logging.error(f"レースデータファイルが見つかりません。パターン: {pattern}")
        return None

    logging.debug(f"見つかったファイル: {files[0]}")
    return Path(files[0])


def process_single_race_file(race_file: Path, race_processor: RaceProcessor) -> <PERSON>ple[List[pd.DataFrame], Set[str]]:
    """単一のレースファイルを処理する

    Args:
        race_file (Path): レースファイルのパス
        race_processor (RaceProcessor): レースプロセッサのインスタンス

    Returns:
        Tuple[List[pd.DataFrame], Set[str]]: (レース結果のリスト, 馬IDのセット)
    """
    race_results = []
    horse_ids = set()

    try:
        with open(race_file, "rb") as f:
            year_races = pickle.load(f)

        logging.info(f"ファイル読み込み完了: {race_file}")

        # DataFrameの場合の処理
        if isinstance(year_races, pd.DataFrame):
            logging.info(f"{len(year_races)}行のDataFrameを処理中")

            # レースIDごとにグループ化して処理
            for race_id, race_group in tqdm(year_races.groupby("race_id"),
                                          desc=f"レース処理", leave=False):
                try:
                    race_data = {
                        "result_table": race_group.to_dict("records"),
                        "race_date": (
                            race_group["race_date"].iloc[0] if "race_date" in race_group.columns else ""
                        ),
                        "race_name": (
                            race_group["race_name"].iloc[0] if "race_name" in race_group.columns else ""
                        ),
                        "race_course": (
                            race_group["race_course"].iloc[0] if "race_course" in race_group.columns else ""
                        ),
                        "weather": race_group["weather"].iloc[0] if "weather" in race_group.columns else "",
                        "ground_state": (
                            race_group["ground_state"].iloc[0] if "ground_state" in race_group.columns else ""
                        ),
                        "race_type": (
                            race_group["race_type"].iloc[0] if "race_type" in race_group.columns else ""
                        ),
                    }

                    race_result, race_horse_ids = process_single_race(race_id, race_data)
                    if race_result is not None:
                        race_results.append(race_result)
                        horse_ids.update(race_horse_ids)

                except Exception as e:
                    logging.error(f"レース{race_id}の処理中にエラー: {str(e)}", exc_info=True)

        # 辞書形式の場合の処理（並列処理）
        elif isinstance(year_races, dict):
            logging.info(f"{len(year_races)}件のレースを並列処理中")

            with ThreadPoolExecutor(max_workers=4) as executor:
                future_to_race = {
                    executor.submit(process_single_race, race_id, race_data): race_id
                    for race_id, race_data in year_races.items()
                }

                for future in tqdm(as_completed(future_to_race),
                                 total=len(future_to_race), desc="レース処理(並列)", leave=False):
                    race_id = future_to_race[future]
                    try:
                        race_result, race_horse_ids = future.result()
                        if race_result is not None:
                            race_results.append(race_result)
                            horse_ids.update(race_horse_ids)
                    except Exception as e:
                        logging.error(f"レース{race_id}の処理中にエラー: {str(e)}", exc_info=True)
        else:
            logging.error(f"未対応のデータ型です: {type(year_races)}")

    except Exception as e:
        logging.error(f"レースファイル{race_file}の読み込み中にエラー: {str(e)}", exc_info=True)

    return race_results, horse_ids


def process_single_race(race_id: str, race_data: Dict) -> Tuple[Optional[pd.DataFrame], Set[str]]:
    """単一のレース情報を処理する

    Args:
        race_id (str): レースID
        race_data (Dict): レース情報の辞書

    Returns:
        Tuple[Optional[pd.DataFrame], Set[str]]: (レース結果DataFrame, 馬IDのセット)
    """
    try:
        # レース結果テーブルから馬IDを抽出
        horse_ids = set()
        result_table = race_data.get("result_table", [])

        for row in result_table:
            if "horse_id" in row and row["horse_id"]:
                horse_ids.add(str(row["horse_id"]))

        # レース結果をDataFrameに変換
        if result_table:
            race_df = pd.DataFrame(result_table)
            race_df["race_id"] = race_id

            # レース情報を追加
            for key in ["race_date", "race_name", "race_course", "weather", "ground_state", "race_type"]:
                if key in race_data:
                    race_df[key] = race_data[key]

            return race_df, horse_ids
        else:
            logging.warning(f"レース{race_id}に結果テーブルがありません")
            return None, set()

    except Exception as e:
        logging.error(f"レース{race_id}の処理中にエラー: {str(e)}", exc_info=True)
        return None, set()


def save_processed_data(race_results: pd.DataFrame, horse_ids: Set[str],
                       output_dir: str, prefix: str = "processed_races") -> str:
    """処理済みデータを保存する

    Args:
        race_results (pd.DataFrame): レース結果のDataFrame
        horse_ids (Set[str]): 馬IDのセット
        output_dir (str): 出力ディレクトリ
        prefix (str): ファイル名のプレフィックス

    Returns:
        str: 保存されたファイルのパス
    """
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_path = output_path / f"{prefix}_{timestamp}.pickle"

    with open(file_path, "wb") as f:
        pickle.dump({
            "race_results": race_results,
            "horse_ids": list(horse_ids)
        }, f)

    logging.info(f"処理済みデータを保存しました: {file_path}")
    return str(file_path)


def process_race_bin_to_pickle_batch(years: List[str],
                                   bin_base_dir: str = "F:\\keiba__AI_2025\\data\\html\\race\\race_by_year",
                                   output_dir: str = "F:\\keiba__AI_2025\\data\\processed",
                                   parallel: bool = True,
                                   max_workers: int = 4) -> None:
    """binファイルからpickleファイルへのバッチ変換処理（元のノートブックコードベース）

    Args:
        years (List[str]): 処理対象年度のリスト
        bin_base_dir (str): binファイルのベースディレクトリ
        output_dir (str): 出力ディレクトリ
        parallel (bool): 並列処理を使用するかどうか
        max_workers (int): 並列処理のワーカー数
    """
    # ログ設定
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # レースプロセッサを初期化
    processor = RaceProcessor()

    # 出力ディレクトリを作成
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    for year in years:
        print(f'{year}年のbinファイルを処理中...')
        logging.info(f'{year}年のbinファイル処理を開始')

        bin_dir = f"{bin_base_dir}\\{year}"
        bin_files = glob.glob(f'{bin_dir}\\*.bin')

        if not bin_files:
            print(f'  {year}年のbinファイルが見つかりません: {bin_dir}')
            logging.warning(f'{year}年のbinファイルが見つかりません: {bin_dir}')
            continue

        race_info_list = []
        race_results_list = []

        if parallel and len(bin_files) > 1:
            # 並列処理
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(processor.parse_race_html, bin_file): bin_file
                          for bin_file in bin_files}

                for future in tqdm(as_completed(futures), total=len(futures),
                                 desc=f"{year}年binファイル処理"):
                    bin_file = futures[future]
                    try:
                        info_df, results_df = future.result()
                        if not info_df.empty:
                            race_info_list.append(info_df)
                        if not results_df.empty:
                            race_results_list.append(results_df)
                    except Exception as e:
                        logging.error(f"ファイル {bin_file} の処理中にエラー: {e}")
        else:
            # 逐次処理
            for bin_file in tqdm(bin_files, desc=f"{year}年binファイル処理"):
                try:
                    info_df, results_df = processor.parse_race_html(html_path=bin_file)
                    if not info_df.empty:
                        race_info_list.append(info_df)
                    if not results_df.empty:
                        race_results_list.append(results_df)
                except Exception as e:
                    logging.error(f"ファイル {bin_file} の処理中にエラー: {e}")

        # 年ごとにまとめてDataFrame化
        race_info_df = pd.concat(race_info_list, ignore_index=True) if race_info_list else pd.DataFrame()
        race_results_df = pd.concat(race_results_list, ignore_index=True) if race_results_list else pd.DataFrame()

        # DataFrameが空でなければ保存
        if not race_info_df.empty:
            race_info_df.to_pickle(output_path / f'race_info_{year}.pickle')
            print(f'  race_info_{year}.pickle を保存')
            logging.info(f'race_info_{year}.pickle を保存 ({len(race_info_df)}件)')

        if not race_results_df.empty:
            race_results_df.to_pickle(output_path / f'race_results_{year}.pickle')
            print(f'  race_results_{year}.pickle を保存')
            logging.info(f'race_results_{year}.pickle を保存 ({len(race_results_df)}件)')

        print(f'{year}年のpickle保存完了\n')
        logging.info(f'{year}年の処理完了')

    print('全ての年の処理が完了しました。')
    logging.info('全ての年の処理が完了しました。')


def process_race_data_batch(target_years: List[str], base_dir: str = "data",
                           max_workers: int = 4, save_output: bool = True) -> Tuple[pd.DataFrame, Set[str]]:
    """レースデータのバッチ処理メイン関数（既存のpickleファイルベース）

    Args:
        target_years (List[str]): 処理対象年度のリスト
        base_dir (str): データディレクトリのベースパス
        max_workers (int): 並列処理のワーカー数
        save_output (bool): 結果を保存するかどうか

    Returns:
        Tuple[pd.DataFrame, Set[str]]: (レース結果DataFrame, 馬IDのセット)
    """
    # ログ設定
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # レースプロセッサを初期化
    race_processor = RaceProcessor()

    all_race_results = []
    all_horse_ids = set()
    processed_races_count = 0

    for year in tqdm(target_years, desc="年度別レース処理"):
        logging.info(f"{year}年のレース処理を開始")

        # 最新のレースデータファイルを探す
        race_file = find_latest_race_file(base_dir, year)
        if race_file is None:
            logging.warning(f"{year}年のレースデータファイルが見つかりません")
            continue

        # ファイルを処理
        race_results, horse_ids = process_single_race_file(race_file, race_processor)

        all_race_results.extend(race_results)
        all_horse_ids.update(horse_ids)
        processed_races_count += len(race_results)

    # 全レース結果を結合
    if not all_race_results:
        logging.error(f"正常に処理されたレースがありません。処理試行数: {processed_races_count}")
        raise ValueError("正常に処理されたレースがありません")

    logging.info(f"レース結果を結合中... 処理成功数: {len(all_race_results)}")
    combined_results = pd.concat(all_race_results, ignore_index=True)

    # 結果を保存
    if save_output:
        save_processed_data(combined_results, all_horse_ids,
                          os.path.join(base_dir, "processed"))

    logging.info(f"{len(combined_results)}件のレース結果と{len(all_horse_ids)}頭の馬を処理しました")
    return combined_results, all_horse_ids


if __name__ == "__main__":
    # 使用例1: binファイルからpickleファイルへの変換（元のノートブックコードと同等）
    years = ["2024"]
    process_race_bin_to_pickle_batch(
        years=years,
        bin_base_dir="F:\\keiba__AI_2025\\data\\html\\race\\race_by_year",
        output_dir="F:\\keiba__AI_2025\\data\\processed",
        parallel=True,
        max_workers=4
    )

    # 使用例2: 既存のpickleファイルからの処理
    # target_years = ["2024"]
    # race_results, horse_ids = process_race_data_batch(target_years)
    # print(f"処理完了: {len(race_results)}件のレース結果、{len(horse_ids)}頭の馬")
    # print(f"レース結果の最初の5行:")
    # print(race_results.head())
