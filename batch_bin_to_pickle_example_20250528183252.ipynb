import sys
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

# 最新のプロセッサクラスをインポート
from module.race_batch_processor import process_race_bin_to_pickle_batch
from module.race_horse_targeted_processor import RaceHorseTargetedProcessor
from module.race_data_processor import RaceProcessor
from module.data_merger import DataMerger

import pandas as pd
import numpy as np
from tqdm.notebook import tqdm

# 処理したい年のリストを指定
years = ['2017', '2018', '2019']  # 必要な年を追加してください
# 単一年の場合: years = ['2024']
# 複数年の場合: years = ['2020', '2021', '2022', '2023', '2024']

# データディレクトリの設定
base_dir = Path.cwd()  # 現在のディレクトリを基準
data_dir = base_dir / 'data' / 'html' / 'race' / 'race_by_year'

# 出力ディレクトリの作成
output_dir = Path('output')
output_dir.mkdir(exist_ok=True)

print(f"処理対象年: {years}")
print(f"データディレクトリ: {data_dir}")
print(f"出力ディレクトリ: {output_dir}")

processor = RaceProcessor()

for year in years:
    print(f'{year}年のbinファイルを処理中...')
    bin_dir = data_dir / year
    bin_files = list(bin_dir.glob('*.bin'))
    race_info_list = []
    race_results_list = []
    for bin_file in bin_files:
        info_df, results_df = processor.parse_race_html(html_path=bin_file)
        if not info_df.empty:
            race_info_list.append(info_df)
        if not results_df.empty:
            race_results_list.append(results_df)
    # 年ごとにまとめてDataFrame化
    race_info_df = pd.concat(race_info_list, ignore_index=True) if race_info_list else pd.DataFrame()
    race_results_df = pd.concat(race_results_list, ignore_index=True) if race_results_list else pd.DataFrame()
    # DataFrameが空でなければ保存
    if not race_info_df.empty:
        race_info_df.to_pickle(output_dir / f'race_info_{year}.pickle')
        print(f'  race_info_{year}.pickle を保存')
    if not race_results_df.empty:
        race_results_df.to_pickle(output_dir / f'race_results_{year}.pickle')
        print(f'  race_results_{year}.pickle を保存')
    print(f'{year}年のpickle保存完了\n')

print('全ての年の処理が完了しました。')

import module.race_batch_processor
# 使用例1: binファイルからpickleファイルへの変換
years = ["2024"]
module.race_batch_processor.process_race_bin_to_pickle_batch(
    years=years,
    bin_base_dir=data_dir,
    output_dir=output_dir,
    parallel=True,
    max_workers=4
)

from module.race_horse_targeted_processor import RaceHorseTargetedProcessor

# プロセッサ作成
processor = RaceHorseTargetedProcessor()

# 2024年のレースから馬IDを抽出し、馬情報を処理
result = processor.process_race_to_horses(
    year="2022",
    include_basic_info=True,
    include_results=True,
    parallel=True,
    max_workers=4,
    save_output=True
)

race_info = pd.read_pickle(output_dir / "race_info_2024.pickle")
race_info

race_results = pd.read_pickle(output_dir / "race_results_2024.pickle")
race_results

horse_info = pd.read_pickle(output_dir / "race_horses_horse_info_2024.pickle")
horse_info

horse_results = pd.read_pickle(output_dir / "race_horses_horse_results_2024.pickle")
horse_results

# Jupyter Notebookのセル

import sys
import os
import pandas as pd
import logging

# 'module'ディレクトリの親ディレクトリをsys.pathに追加 (環境に応じて調整)
# 例: ノートブックが 'notebooks' フォルダにあり、'module' がその一つ上の階層にある場合
# module_parent_dir = os.path.abspath(os.path.join(os.getcwd(), '..'))
# if module_parent_dir not in sys.path:
#     sys.path.append(module_parent_dir)

from module.data_merger import DataMerger
from module.race_data_processor import RaceProcessor
from module.horse_processor import HorseProcessor
from module.constants import LocalPaths # 必要に応じて

# ロギング設定 (任意)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# Jupyter Notebookのセル

# RaceProcessorのインスタンスを作成
race_processor = RaceProcessor()

# 例: 特定の年のレースデータを処理 (実際のファイルパスや処理に合わせてください)
# process_race_bin_files は (race_info_df, race_results_df) を返しますが、
# DataMerger は race_processor.preprocess_data() を内部で呼び出すことを期待しているようです。
# preprocess_data() が適切なデータを返すように、事前に process_race_bin_files を実行しておくか、
# または RaceProcessor のインスタンス変数 _race_info_df, _race_results_df が設定されるようにします。
logger.info("RaceProcessorでレースデータを処理中...")
# 例: 2023年のデータを処理
# 実際の .bin ファイルの場所に応じて、process_race_bin_files の引数を調整してください。
# LocalPaths.HTML_RACE_DIR が "data/html/race/race_by_year" を指す場合、
# year="2023" とすると "data/html/race/race_by_year/2023/*.bin" を処理します。
race_processor.process_race_bin_files(year="2023", parallel=True, max_workers=4) # 仮の年
# これで race_processor._race_info_df と race_processor._race_results_df が設定されます。
# DataMerger の __init__ は race_processor.preprocess_data() を呼び出します。
logger.info("RaceProcessorのデータ準備完了。")
# Jupyter Notebookのセル
# HorseProcessorのインスタンスを作成
# 例1: Pickleファイルから馬の過去成績と基本情報を読み込む場合
#      実際のファイルパスに置き換えてください。
# horse_results_filepath = "f:/keiba__AI_2025/data/processed/horse_results_2023.pickle" # 仮
# horse_info_filepath = "f:/keiba__AI_2025/data/processed/horse_info_2023.pickle"       # 仮
#
# horse_processor = HorseProcessor(
#     horse_results_filepath=horse_results_filepath,
#     horse_info_filepath=horse_info_filepath
# )

# 例2: HTMLファイルから馬の過去成績と基本情報を処理する場合
#      DataMerger は horse_processor.preprocessed_data (過去成績) と
#      horse_processor から取得できる馬基本情報 (間接的に _horse_info_data) を期待します。
logger.info("HorseProcessorで馬データを準備中...")
horse_processor = HorseProcessor() # データは後からロードまたは処理

# 馬の過去成績をHTMLから取得・前処理 (例: 2023年のデータ)
# get_all_horse_results は前処理済みのDataFrameを返しますが、
# HorseProcessor の preprocessed_data プロパティが更新されるように、
# 結果をインスタンスの _raw_data に設定し、_preprocess を呼び出すか、
# または HorseProcessor のコンストラクタで horse_results_data として渡す必要があります。
# ここでは、コンストラクタでデータを渡すアプローチが DataMerger の期待と整合しやすいです。

# ダミーのDataFrameまたは実際のデータパスを使用してください。
# 以下は、HorseProcessorがHTMLからデータを取得し、内部状態を更新する例です。
# 実際には、HorseProcessorのコンストラクタにファイルパスを渡すか、
# 取得したDataFrameをコンストラクタの horse_results_data や horse_info_data に渡すのが一般的です。

# 例として、特定の馬のIDリストで情報を取得する場合
# horse_ids_for_processor = ['2020100001', '2020100002'] # 仮のID
# results_df = horse_processor.process_horse_results_for_ids(horse_ids=horse_ids_for_processor)
# info_df = horse_processor.process_horse_info_for_ids(horse_ids=horse_ids_for_processor)
# horse_processor = HorseProcessor(horse_results_data=results_df, horse_info_data=info_df)

# より簡単なのは、ファイルパスをコンストラクタに渡すことです。
# 上記のコメントアウトされた例1を参照してください。
# ここでは、DataMerger の __init__ が horse_processor.preprocessed_data (過去成績) と
# horse_processor._horse_info_data (基本情報) を参照することを期待していると仮定し、
# コンストラクタでファイルパスを指定して初期化するのが最も素直です。
# (もしファイルがない場合は、ダミーデータで試すか、HorseProcessorのデータ取得メソッドを先に実行してください)

# ダミーのファイルパス（実際にファイルが存在するようにしてください）
dummy_horse_results_path = "dummy_horse_results.pkl"
dummy_horse_info_path = "dummy_horse_info.pkl"
if not os.path.exists(dummy_horse_results_path):
    pd.DataFrame({'horse_id': ['1'], 'date': [pd.to_datetime('2023-01-01')], '着順': [1]}).set_index('horse_id').to_pickle(dummy_horse_results_path)
if not os.path.exists(dummy_horse_info_path):
    pd.DataFrame({'horse_id': ['1'], '生年月日': [pd.to_datetime('2020-01-01')]}).set_index('horse_id').to_pickle(dummy_horse_info_path)

horse_processor = HorseProcessor(
    horse_results_filepath=dummy_horse_results_path, # 実際のパスに置き換えてください
    horse_info_filepath=dummy_horse_info_path        # 実際のパスに置き換えてください
)
logger.info("HorseProcessorのデータ準備完了。")



# Jupyter Notebookのセル

# DataMergerのインスタンスを作成
# target_colsやgroup_colsは必要に応じて指定してください
# (指定しない場合はDataMergerConstantsのデフォルト値が使われます)
data_merger = DataMerger(
    race_processor=race_processor,
    horse_processor=horse_processor
    # target_cols=['着順', '人気', '斤量'], # 例: 集計したい過去成績の列
    # group_cols=['開催', 'race_type', '距離'] # 例: ターゲットエンコーディング時のグループ化列
)
logger.info("DataMergerのインスタンス化完了。")


# Jupyter Notebookのセル

logger.info("DataMergerによるデータマージ処理を開始...")
data_merger.merge()
logger.info("データマージ処理完了。")


# Jupyter Notebookのセル

# マージされたデータを取得
merged_df = data_merger.merged_data

# 結果の確認
if not merged_df.empty:
    logger.info("マージされたデータの先頭5行:")
    print(merged_df.head())
    logger.info(f"\nマージされたデータの行数: {len(merged_df)}")
    logger.info(f"マージされたデータのカラム数: {len(merged_df.columns)}")
    logger.info(f"マージされたデータのカラム一覧: {merged_df.columns.tolist()}")

    # 処理概要の取得
    summary = data_merger.get_summary_info()
    logger.info("\n処理概要:")
    for key, value in summary.items():
        if isinstance(value, list) and len(value) > 10: # 長いリストは省略表示
            logger.info(f"  {key}: (最初の10件) {value[:10]}...")
        else:
            logger.info(f"  {key}: {value}")
else:
    logger.warning("マージ処理の結果、データは空でした。")
    logger.warning("RaceProcessor や HorseProcessor のデータ準備、または DataMerger の __init__ でのデータ取得部分を確認してください。")
    # DataMerger の __init__ での _horse_info の設定がうまくいかない場合、
    # ここで merged_df が期待通りでない可能性があります。


import pandas as pd
import logging
import json
import os

# 独自モジュールのインポート
from module.comprehensive_data_integrator import ComprehensiveDataIntegrator, ComprehensiveIntegratorConfig
from module.constants import LocalPaths # データパスの確認用

# Jupyter Notebook上でログ出力を表示するための設定 (任意)
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler()])


my_config_dict = {
    "default_year": "2023",
    # "default_years": ["2022", "2023"], # 単一年度か複数年度か、どちらかを指定
    "include_race_info": True,
    "include_horse_info": True,
    "include_past_performance": True,
    "performance_window_races": [5, 10],
    "parallel": True,
    "max_workers": os.cpu_count() or 1, # CPUコア数をデフォルトに
    "save_output": False # Notebook上ではまずFalseにして結果を確認することが多い
}
# integrator = ComprehensiveDataIntegrator(config=my_config_dict)


config_path = "F:\\keiba__AI_2025\\module\\config.json" # 実際のパスに置き換えてください
config_from_file = {}
if os.path.exists(config_path):
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_from_file = json.load(f)
        logging.info(f"設定ファイル {config_path} を読み込みました。")
    except Exception as e:
        logging.error(f"設定ファイル {config_path} の読み込みに失敗: {e}")
else:
    logging.warning(f"設定ファイル {config_path} が見つかりません。デフォルト設定を使用します。")

# ComprehensiveDataIntegrator のインスタンス化時に設定を渡す
# integrator = ComprehensiveDataIntegrator(config=config_from_file)


# %% [markdown]
# # ComprehensiveDataIntegrator の利用サンプル
# 
# このノートブックでは、`ComprehensiveDataIntegrator` を使用して、
# レース情報、レース結果、馬の基本情報、馬の過去成績を統合した
# 包括的なデータテーブルを生成する方法を示します。

# %%
import os
import pandas as pd
import logging

# 必要なモジュールをインポート
# (プロジェクトのルートディレクトリからの相対パスで指定することを想定)
# 必要に応じて sys.path にプロジェクトのパスを追加してください。
# import sys
# sys.path.append('F:/keiba__AI_2025') # ご自身のプロジェクトパスに合わせてください

from module.comprehensive_data_integrator import ComprehensiveDataIntegrator, load_config_from_file, merge_settings
from module.constants import LocalPaths, ComprehensiveIntegratorConfig # 設定クラスもインポート

# %% [markdown]
# ## 1. ロギング設定
# 
# 詳細な処理状況を確認するためにロギングを設定します。

# %%
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler()])
logger = logging.getLogger() # ルートロガーを取得
logger.setLevel(logging.INFO) # INFOレベル以上のログを出力
# --- 設定の選択 (上記いずれかの方法で config_dict を準備) ---
# 例: Python辞書で設定する場合
config_dict_for_integrator = {
    # "default_year": "2023", # generate_comprehensive_table の引数で指定する場合は不要
    "default_years": ["2022"],
    "use_pickle_source": True, # pickleファイルから読み込むことを指定
    "pickle_base_dir": output_dir, #pickleファイルが格納されているディレクトリ
    #各pickleファイルのファイル名テンプレート (デフォルト値の例)
    "race_info_filename_template": "race_info_{year}.pickle", #RaceProcessor側でデフォルトを持つ想定
    "race_results_filename_template": "race_results_{year}.pickle", #RaceProcessor側でデフォルトを持つ想定
    "horse_info_pickle_filename_template": "race_horses_horse_info_{year}.pickle", #ComprehensiveIntegratorConfigで定義可能
    "horse_results_pickle_filename_template": "race_horses_horse_results_{year}.pickle", #ComprehensiveIntegratorConfigで定義可能

    # カスタム特徴量pickle (使用しない場合は enable_custom_feature_pickle: False)
    "enable_custom_feature_pickle": False,
    # "custom_feature_pickle_filename_template": "custom_features_{year}.pickle",

    "include_race_info": True,
    "include_horse_info": True,
    "include_past_performance": True,
    "performance_window_races": [3, 5, 10], # 直近3, 5, 10レースの成績を集計
    "parallel": True,
    "max_workers": os.cpu_count() or 1, # 環境に合わせて調整
    "save_output": False # NotebookではまずFalseで結果確認
}

# 設定ファイル (config.json) があればそれも読み込む (オプション)
# config_file_path = "config.json" 
# file_config = load_config_from_file(config_file_path)
# final_settings = merge_settings(file_config, argparse.Namespace(**config_dict_for_integrator)) # argparse.Namespaceでラップ

# ここでは直接 config_dict_for_integrator を使用
final_settings = config_dict_for_integrator

print("\n最終的な実行設定:")
for key, value in final_settings.items():
    print(f"  {key}: {value}")

# %% [markdown]
# ## 3. `ComprehensiveDataIntegrator` のインスタンス化
# 
# 準備した設定で `ComprehensiveDataIntegrator` のインスタンスを作成します。

# %%
try:
    integrator = ComprehensiveDataIntegrator(config=final_settings)
    print("\nComprehensiveDataIntegrator のインスタンス化に成功しました。")
except Exception as e:
    logger.error(f"ComprehensiveDataIntegrator のインスタンス化中にエラーが発生しました: {e}", exc_info=True)
    integrator = None # エラー時は None にしておく

# %% [markdown]
# ## 4. `generate_comprehensive_table` メソッドの実行
# 
# 包括的なデータテーブルを生成します。
# `years` 引数で処理対象の年度を明示的に指定することも可能です。
# 
# **注意:** 初回実行時やHTMLからデータを取得する場合、時間がかかることがあります。
# pickleファイルが存在しない場合、`HorseProcessor` や `RaceProcessor` がHTMLからの読み込みにフォールバックしようとします。
# その際にHTMLファイルが存在しないと、データが空になる可能性があります。
# 
# 事前に `RaceDataProcessor` や `HorseDataProcessor` のスクリプトを実行して、
# 必要なpickleファイル (`race_info_{year}.pickle`, `race_results_{year}.pickle`, `horse_info_{year}.pickle`, `horse_results_{year}.pickle` など) を
# `pickle_base_dir` で指定したディレクトリに配置しておいてください。

# %%
if integrator:
    try:
        # generate_comprehensive_table の引数で設定を上書きすることも可能
        # ここではインスタンス化時の設定を使用
        comprehensive_df = integrator.generate_comprehensive_table(
            # years=["2023"], # 特定の年だけ処理したい場合
            # use_pickle_source=True # メソッド呼び出し時にも指定可能
        )
        
        print("\n`generate_comprehensive_table` の実行が完了しました。")
    except Exception as e:
        logger.error(f"`generate_comprehensive_table` の実行中にエラーが発生しました: {e}", exc_info=True)
        comprehensive_df = pd.DataFrame() # エラー時は空のDataFrame
else:
    print("Integratorのインスタンスが作成されていないため、処理をスキップします。")
    comprehensive_df = pd.DataFrame()

# %% [markdown]
# ## 5. 結果の確認と利用

# %%
if not comprehensive_df.empty:
    print(f"\n✅ 包括的データ生成完了:")
    print(f"   データ件数: {len(comprehensive_df):,}件")
    print(f"   カラム数: {len(comprehensive_df.columns)}個")
    if 'race_id' in comprehensive_df.columns:
        print(f"   ユニークレース数: {comprehensive_df['race_id'].nunique()}")
    if 'horse_id' in comprehensive_df.columns:
        print(f"   ユニーク馬数: {comprehensive_df['horse_id'].nunique()}")

    print("\n📋 サンプルデータ (先頭5行):")
    display(comprehensive_df.head())

    print("\n📊 データ概要:")
    summary = integrator.get_data_summary()
    for key, value in summary.items():
        if key == "data_columns":
            print(f"  {key}:")
            for cat, cols in value.items():
                print(f"    {cat}: {len(cols)}個")
        elif key == "missing_data_ratio":
            print(f"  {key} (上位5件):")
            sorted_missing = sorted(value.items(), key=lambda item: item[1], reverse=True)
            for col, ratio in sorted_missing[:5]:
                if ratio > 0:
                    print(f"    {col}: {ratio:.2%}")
        else:
            print(f"  {key}: {value}")
    
    # カラム一覧
    # print("\nカラム一覧:")
    # for col in comprehensive_df.columns:
    #     print(f"- {col}")

else:
    print("\n❌ 生成されたデータは空です。ログを確認してエラーの原因を調査してください。")
    print("考えられる原因:")
    print("- 指定したpickleファイルが存在しない、またはパスが間違っている。")
    print("- pickleファイルの中身が空、または期待する形式ではない。")
    print("- HTMLからの読み込みにフォールバックしたが、HTMLファイルが存在しない。")
    print("- データ処理中に何らかのエラーが発生した。")

# %% [markdown]
# ## 6. (オプション) 結果のファイル保存
# 
# 生成されたデータをCSVやPickle形式で保存します。

# %%
# save_output を True にして実行する場合
# if integrator and not comprehensive_df.empty and final_settings.get("save_output", False):
#     try:
#         current_years = final_settings.get('default_years', [])
#         if isinstance(current_years, list) and len(current_years) > 1:
#             year_range_str = f"{min(current_years)}-{max(current_years)}"
#             filename_prefix_for_save = f"{final_settings.get('filename_prefix', integrator.config.filename_prefix)}_multi_year_{year_range_str}"
#             save_year_param = None
#         else:
#             filename_prefix_for_save = final_settings.get('filename_prefix', integrator.config.filename_prefix)
#             save_year_param = current_years[0] if isinstance(current_years, list) and current_years else final_settings.get('default_year', integrator.config.default_year)
        
#         pickle_path, csv_path = integrator.save_comprehensive_table(
#             filename_prefix=filename_prefix_for_save,
#             year=save_year_param, # 単一年度の場合のみ年を指定
#             save_pickle=True,
#             save_csv=True
#         )
#         print("\n💾 ファイル保存完了:")
#         if pickle_path:
#             print(f"   Pickle: {os.path.abspath(pickle_path)}")
#         if csv_path:
#             print(f"   CSV: {os.path.abspath(csv_path)}")
#     except Exception as e:
#         logger.error(f"ファイル保存中にエラー: {e}", exc_info=True)
# elif integrator and comprehensive_df.empty:
#     print("データが空のため、ファイル保存はスキップされました。")
# else:
#     print("ファイル保存は設定されていません (save_output=False)。")

# %% [markdown]
# ---
# これで、`ComprehensiveDataIntegrator` を使用してデータを統合し、結果を確認する基本的な流れは完了です。
# 設定や処理対象のデータを変更して、さまざまなパターンのデータ統合を試してみてください。

comprehensive_df_single_year

