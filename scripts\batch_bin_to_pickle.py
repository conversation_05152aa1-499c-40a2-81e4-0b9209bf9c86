import sys
import os
from pathlib import Path

# スクリプト自身のディレクトリを基準にmoduleをパス追加
script_dir = Path(__file__).resolve().parent
module_dir = script_dir.parent / "module"
sys.path.append(str(module_dir))
from race_data_processor import RaceProcessor

# 変換したい年のリスト
years = ["2020", "2021", "2022"]  # 必要な年を追加

# 出力ディレクトリ
output_dir = script_dir.parent / "output"
os.makedirs(output_dir, exist_ok=True)

processor = RaceProcessor()

for year in years:
    print(f"{year}年のbinファイルを処理中...")
    race_info_df, race_results_df = processor.process_race_bin_files(year=year)
    # DataFrameが空でなければ保存
    if not race_info_df.empty:
        race_info_df.to_pickle(output_dir / f"race_info_{year}.pickle")
        print(f"  race_info_{year}.pickle を保存")
    if not race_results_df.empty:
        race_results_df.to_pickle(output_dir / f"race_results_{year}.pickle")
        print(f"  race_results_{year}.pickle を保存")
    print(f"{year}年のpickle保存完了\n")

print("全ての年の処理が完了しました。")
