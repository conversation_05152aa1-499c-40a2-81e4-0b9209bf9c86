{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 改善版：競馬データ一括処理Notebook\n", "\n", "このNotebookは、最新の統合プロセッサクラスを使用して、\n", "複数年分のbinファイルをDataFrameに変換し、レース情報と馬情報を統合処理します。\n", "\n", "## 主な改善点\n", "- 最新の`RaceHorseTargetedProcessor`を使用\n", "- エラーハンドリングとログ機能の強化\n", "- 並列処理の最適化\n", "- 進捗表示の改善\n", "- 設定の外部化\n", "- 結果の検証機能"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import logging\n", "from pathlib import Path\n", "from typing import Dict, List, Optional, Any\n", "import json\n", "from datetime import datetime\n", "\n", "# 最新のプロセッサクラスをインポート\n", "from module.race_batch_processor import process_race_bin_to_pickle_batch\n", "from module.race_horse_targeted_processor import RaceHorseTargetedProcessor\n", "from module.race_data_processor import RaceProcessor\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from tqdm.notebook import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 設定とログ初期化"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-28 19:26:19,078 - __main__ - INFO - 処理設定: {\n", "  \"target_years\": \"['2024']\",\n", "  \"base_dir\": \"f:\\\\keiba__AI_2025\",\n", "  \"data_dir\": \"f:\\\\keiba__AI_2025\\\\data\\\\html\\\\race\\\\race_by_year\",\n", "  \"output_dir\": \"output\",\n", "  \"parallel\": \"True\",\n", "  \"max_workers\": \"4\",\n", "  \"include_race_info\": \"True\",\n", "  \"include_race_results\": \"True\",\n", "  \"include_horse_info\": \"True\",\n", "  \"include_horse_results\": \"True\",\n", "  \"save_intermediate\": \"True\",\n", "  \"validate_results\": \"True\"\n", "}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["処理対象年: ['2024']\n", "データディレクトリ: f:\\keiba__AI_2025\\data\\html\\race\\race_by_year\n", "出力ディレクトリ: output\n"]}], "source": ["# ログ設定\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n", "    handlers=[\n", "        logging.StreamHandler(),\n", "        logging.FileHandler(f'batch_processing_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.log')\n", "    ]\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "# 処理設定\n", "CONFIG = {\n", "    # 処理対象年度（必要に応じて変更）\n", "    'target_years': ['2024'],  # 例: ['2022', '2023', '2024']\n", "    \n", "    # ディレクトリ設定\n", "    'base_dir': Path.cwd(),\n", "    'data_dir': Path.cwd() / 'data' / 'html' / 'race' / 'race_by_year',\n", "    'output_dir': Path('output'),\n", "    \n", "    # 並列処理設定\n", "    'parallel': True,\n", "    'max_workers': 4,\n", "    \n", "    # 処理オプション\n", "    'include_race_info': True,\n", "    'include_race_results': True,\n", "    'include_horse_info': True,\n", "    'include_horse_results': True,\n", "    \n", "    # 保存設定\n", "    'save_intermediate': True,  # 中間結果を保存するか\n", "    'validate_results': True,   # 結果を検証するか\n", "}\n", "\n", "# 出力ディレクトリの作成\n", "CONFIG['output_dir'].mkdir(exist_ok=True)\n", "\n", "logger.info(f\"処理設定: {json.dumps({k: str(v) for k, v in CONFIG.items()}, indent=2, ensure_ascii=False)}\")\n", "print(f\"処理対象年: {CONFIG['target_years']}\")\n", "print(f\"データディレクトリ: {CONFIG['data_dir']}\")\n", "print(f\"出力ディレクトリ: {CONFIG['output_dir']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 方法1: 最新のバッチプロセッサを使用したレースデータ処理"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def validate_data_quality(df: pd.DataFrame, data_type: str) -> Dict[str, Any]:\n", "    \"\"\"データ品質を検証する関数\"\"\"\n", "    validation_result = {\n", "        'data_type': data_type,\n", "        'total_rows': len(df),\n", "        'total_columns': len(df.columns),\n", "        'null_counts': df.isnull().sum().to_dict(),\n", "        'duplicate_rows': df.duplicated().sum(),\n", "        'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024\n", "    }\n", "    \n", "    # データタイプ固有の検証\n", "    if data_type == 'race_info':\n", "        if 'race_id' in df.columns:\n", "            validation_result['unique_race_ids'] = df['race_id'].nunique()\n", "            validation_result['duplicate_race_ids'] = df['race_id'].duplicated().sum()\n", "    elif data_type == 'race_results':\n", "        if 'horse_id' in df.columns:\n", "            validation_result['unique_horse_ids'] = df['horse_id'].nunique()\n", "        if 'race_id' in df.columns:\n", "            validation_result['unique_race_ids'] = df['race_id'].nunique()\n", "    \n", "    return validation_result"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-28 19:26:24,035 - root - INFO - 2024年のbinファイル処理を開始\n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== 方法1: 最新のバッチプロセッサを使用したレースデータ処理 ===\n", "処理対象年度: ['2024']\n", "2024年のbinファイルを処理中...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024年binファイル処理: 100%|██████████| 3454/3454 [10:40<00:00,  5.40it/s]\n", "2025-05-28 19:37:05,881 - root - INFO - race_info_2024.pickle を保存 (3454件)\n", "2025-05-28 19:37:06,029 - root - INFO - race_results_2024.pickle を保存 (47181件)\n", "2025-05-28 19:37:06,031 - root - INFO - 2024年の処理完了\n", "2025-05-28 19:37:06,031 - root - INFO - 全ての年の処理が完了しました。\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  race_info_2024.pickle を保存\n", "  race_results_2024.pickle を保存\n", "2024年のpickle保存完了\n", "\n", "全ての年の処理が完了しました。\n", "バッチプロセッサによる処理完了\n", "\n", "2024年の結果を確認中...\n", "  race_info: 3,454行, 9列\n", "  race_results: 47,181行, 25列\n", "  2024年のデータ読み込み完了\n", "\n", "=== 方法1の処理完了 ===\n"]}], "source": ["# 方法1: 最新のバッチプロセッサを使用\n", "print(\"=== 方法1: 最新のバッチプロセッサを使用したレースデータ処理 ===\")\n", "\n", "race_results = {}\n", "validation_reports = {}\n", "\n", "try:\n", "    # バッチプロセッサを使用してレースデータを処理\n", "    # 注意: この関数は年度リストを受け取り、pickleファイルを直接保存します\n", "    print(f\"処理対象年度: {CONFIG['target_years']}\")\n", "    \n", "    process_race_bin_to_pickle_batch(\n", "        years=CONFIG['target_years'],\n", "        bin_base_dir=str(CONFIG['data_dir']),\n", "        output_dir=str(CONFIG['output_dir']),\n", "        parallel=CONFIG['parallel'],\n", "        max_workers=CONFIG['max_workers']\n", "    )\n", "    \n", "    print(\"バッチプロセッサによる処理完了\")\n", "    \n", "    # 生成されたpickleファイルを読み込んで検証\n", "    for year in CONFIG['target_years']:\n", "        print(f\"\\n{year}年の結果を確認中...\")\n", "        year_data = {}\n", "        \n", "        # race_info と race_results のpickleファイルを読み込み\n", "        for data_type in ['race_info', 'race_results']:\n", "            pickle_path = CONFIG['output_dir'] / f\"{data_type}_{year}.pickle\"\n", "            if pickle_path.exists():\n", "                try:\n", "                    df = pd.read_pickle(pickle_path)\n", "                    year_data[data_type] = df\n", "                    print(f\"  {data_type}: {len(df):,}行, {len(df.columns)}列\")\n", "                    \n", "                    # データ品質検証\n", "                    if CONFIG['validate_results']:\n", "                        if year not in validation_reports:\n", "                            validation_reports[year] = {}\n", "                        validation_reports[year][data_type] = validate_data_quality(df, data_type)\n", "                        \n", "                except Exception as e:\n", "                    print(f\"  エラー: {pickle_path} の読み込みに失敗 - {str(e)}\")\n", "            else:\n", "                print(f\"  警告: {pickle_path} が見つかりません\")\n", "        \n", "        if year_data:\n", "            race_results[year] = year_data\n", "            print(f\"  {year}年のデータ読み込み完了\")\n", "        else:\n", "            print(f\"  警告: {year}年のデータが見つかりませんでした\")\n", "    \n", "except Exception as e:\n", "    logger.error(f\"バッチプロセッサの処理中にエラーが発生: {str(e)}\")\n", "    print(f\"エラー: バッチプロセッサの処理に失敗しました - {str(e)}\")\n", "\n", "print(\"\\n=== 方法1の処理完了 ===\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.5. 方法1B: RaceProcessorを直接使用した処理（代替案）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 方法1B: RaceProcessorを直接使用（より細かい制御が可能）\n", "print(\"=== 方法1B: RaceProcessorを直接使用した処理 ===\")\n", "\n", "race_results_direct = {}\n", "\n", "# RaceProcessorのインスタンス作成\n", "race_processor = RaceProcessor()\n", "\n", "for year in CONFIG['target_years']:\n", "    print(f\"\\n{year}年のデータを直接処理中...\")\n", "    \n", "    try:\n", "        # 年度別データディレクトリ\n", "        year_dir = CONFIG['data_dir'] / year\n", "        \n", "        if not year_dir.exists():\n", "            print(f\"警告: {year_dir} が存在しません\")\n", "            continue\n", "        \n", "        # binファイルのリストを取得\n", "        bin_files = list(year_dir.glob('*.bin'))\n", "        print(f\"  発見されたbinファイル数: {len(bin_files)}\")\n", "        \n", "        if not bin_files:\n", "            print(f\"  {year}年のbinファイルが見つかりません\")\n", "            continue\n", "        \n", "        # 各binファイルを処理\n", "        race_info_list = []\n", "        race_results_list = []\n", "        \n", "        for i, bin_file in enumerate(bin_files, 1):\n", "            if i % 100 == 0:  # 進捗表示\n", "                print(f\"  処理中: {i}/{len(bin_files)}\")\n", "                \n", "            try:\n", "                info_df, results_df = race_processor.parse_race_html(html_path=bin_file)\n", "                if not info_df.empty:\n", "                    race_info_list.append(info_df)\n", "                if not results_df.empty:\n", "                    race_results_list.append(results_df)\n", "            except Exception as e:\n", "                logger.warning(f\"ファイル {bin_file.name} の処理中にエラー: {str(e)}\")\n", "                continue\n", "        \n", "        # 年ごとにまとめてDataFrame化\n", "        race_info_df = pd.concat(race_info_list, ignore_index=True) if race_info_list else pd.DataFrame()\n", "        race_results_df = pd.concat(race_results_list, ignore_index=True) if race_results_list else pd.DataFrame()\n", "        \n", "        year_data = {}\n", "        \n", "        # DataFrameが空でなければ保存\n", "        if not race_info_df.empty:\n", "            year_data['race_info'] = race_info_df\n", "            if CONFIG['save_intermediate']:\n", "                race_info_path = CONFIG['output_dir'] / f'race_info_{year}_direct.pickle'\n", "                race_info_df.to_pickle(race_info_path)\n", "                print(f\"  保存完了: race_info_{year}_direct.pickle (行数: {len(race_info_df):,})\")\n", "        else:\n", "            print(f\"  警告: race_info_{year} のデータが空です\")\n", "            \n", "        if not race_results_df.empty:\n", "            year_data['race_results'] = race_results_df\n", "            if CONFIG['save_intermediate']:\n", "                race_results_path = CONFIG['output_dir'] / f'race_results_{year}_direct.pickle'\n", "                race_results_df.to_pickle(race_results_path)\n", "                print(f\"  保存完了: race_results_{year}_direct.pickle (行数: {len(race_results_df):,})\")\n", "        else:\n", "            print(f\"  警告: race_results_{year} のデータが空です\")\n", "        \n", "        if year_data:\n", "            race_results_direct[year] = year_data\n", "            print(f\"  {year}年の直接処理完了\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"{year}年の直接処理中にエラーが発生: {str(e)}\")\n", "        print(f\"エラー: {year}年の直接処理に失敗しました - {str(e)}\")\n", "        continue\n", "\n", "print(\"\\n=== 方法1Bの処理完了 ===\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 方法2: 統合プロセッサを使用した処理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 方法2: 統合プロセッサを使用\n", "print(\"=== 方法2: 統合プロセッサを使用した処理 ===\")\n", "\n", "integrated_results = {}\n", "\n", "for year in CONFIG['target_years']:\n", "    print(f\"\\n{year}年のデータを統合プロセッサで処理中...\")\n", "    \n", "    try:\n", "        # 統合プロセッサのインスタンス作成\n", "        processor = RaceHorseTargetedProcessor(\n", "            base_dir=CONFIG['base_dir'],\n", "            parallel=CONFIG['parallel'],\n", "            max_workers=CONFIG['max_workers']\n", "        )\n", "        \n", "        # 年度別データディレクトリ\n", "        year_dir = CONFIG['data_dir'] / year\n", "        \n", "        if not year_dir.exists():\n", "            print(f\"警告: {year_dir} が存在しません\")\n", "            continue\n", "        \n", "        # binファイルのリストを取得\n", "        bin_files = list(year_dir.glob('*.bin'))\n", "        print(f\"  発見されたbinファイル数: {len(bin_files)}\")\n", "        \n", "        if not bin_files:\n", "            print(f\"  {year}年のbinファイルが見つかりません\")\n", "            continue\n", "        \n", "        # 統合処理の実行\n", "        year_results = processor.process_multiple_files(\n", "            file_paths=bin_files,\n", "            include_race_info=CONFIG['include_race_info'],\n", "            include_race_results=CONFIG['include_race_results'],\n", "            include_horse_info=CONFIG['include_horse_info'],\n", "            include_horse_results=CONFIG['include_horse_results']\n", "        )\n", "        \n", "        if year_results:\n", "            integrated_results[year] = year_results\n", "            \n", "            # 結果の表示\n", "            for data_type, df in year_results.items():\n", "                if df is not None and not df.empty:\n", "                    print(f\"  {data_type}: {len(df):,}行, {len(df.columns)}列\")\n", "                    \n", "                    # 保存\n", "                    if CONFIG['save_intermediate']:\n", "                        output_path = CONFIG['output_dir'] / f\"{data_type}_{year}_integrated.pickle\"\n", "                        df.to_pickle(output_path)\n", "                        print(f\"    保存完了: {output_path}\")\n", "        \n", "        print(f\"{year}年の統合処理完了\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"{year}年の統合処理中にエラーが発生: {str(e)}\")\n", "        print(f\"エラー: {year}年の統合処理に失敗しました - {str(e)}\")\n", "        continue\n", "\n", "print(\"\\n=== 方法2の処理完了 ===\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 結果の比較と検証"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 結果の比較と検証\n", "print(\"=== 処理結果の比較と検証 ===\")\n", "\n", "for year in CONFIG['target_years']:\n", "    print(f\"\\n{year}年の結果比較:\")\n", "    \n", "    # 方法1の結果\n", "    if year in race_results:\n", "        print(\"  方法1 (バッチプロセッサ):\")\n", "        for data_type, df in race_results[year].items():\n", "            if df is not None and not df.empty:\n", "                print(f\"    {data_type}: {len(df):,}行\")\n", "    \n", "    # 方法1Bの結果\n", "    if 'race_results_direct' in locals() and year in race_results_direct:\n", "        print(\"  方法1B (RaceProcessor直接):\")\n", "        for data_type, df in race_results_direct[year].items():\n", "            if df is not None and not df.empty:\n", "                print(f\"    {data_type}: {len(df):,}行\")\n", "    \n", "    # 方法2の結果\n", "    if year in integrated_results:\n", "        print(\"  方法2 (統合プロセッサ):\")\n", "        for data_type, df in integrated_results[year].items():\n", "            if df is not None and not df.empty:\n", "                print(f\"    {data_type}: {len(df):,}行\")\n", "    \n", "    # 検証レポートの表示\n", "    if CONFIG['validate_results'] and year in validation_reports:\n", "        print(f\"  データ品質レポート ({year}年):\")\n", "        for data_type, report in validation_reports[year].items():\n", "            print(f\"    {data_type}:\")\n", "            print(f\"      - 重複行数: {report['duplicate_rows']}\")\n", "            print(f\"      - メモリ使用量: {report['memory_usage_mb']:.2f}MB\")\n", "            if 'unique_race_ids' in report:\n", "                print(f\"      - ユニークレースID数: {report['unique_race_ids']}\")\n", "            if 'unique_horse_ids' in report:\n", "                print(f\"      - ユニーク馬ID数: {report['unique_horse_ids']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. データの確認とサンプル表示"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 処理されたデータの確認\n", "print(\"=== 処理されたデータの確認 ===\")\n", "\n", "# 最初の年のデータを確認\n", "if CONFIG['target_years'] and CONFIG['target_years'][0] in race_results:\n", "    first_year = CONFIG['target_years'][0]\n", "    year_data = race_results[first_year]\n", "    \n", "    print(f\"\\n{first_year}年のデータサンプル:\")\n", "    \n", "    for data_type, df in year_data.items():\n", "        if df is not None and not df.empty:\n", "            print(f\"\\n--- {data_type} ---\")\n", "            print(f\"形状: {df.shape}\")\n", "            print(f\"列名: {list(df.columns)}\")\n", "            print(\"\\n先頭5行:\")\n", "            display(df.head())\n", "            \n", "            # 基本統計情報\n", "            if len(df.select_dtypes(include=[np.number]).columns) > 0:\n", "                print(\"\\n数値列の基本統計:\")\n", "                display(df.describe())\n", "else:\n", "    print(\"表示可能なデータがありません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 最終的な統合と保存"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 複数年データの統合 ===\n", "単一年のデータのため、統合処理をスキップします\n", "\n", "=== 全ての処理が完了しました ===\n", "出力ディレクトリ: output\n", "保存されたファイル:\n", "  - race_horses_horse_info_2021.pickle\n", "  - race_horses_horse_info_2022.pickle\n", "  - race_horses_horse_info_2023.pickle\n", "  - race_horses_horse_info_2024.pickle\n", "  - race_horses_horse_results_2021.pickle\n", "  - race_horses_horse_results_2022.pickle\n", "  - race_horses_horse_results_2023.pickle\n", "  - race_horses_horse_results_2024.pickle\n", "  - race_info_2017.pickle\n", "  - race_info_2017_test.pickle\n", "  - race_info_2018.pickle\n", "  - race_info_2019.pickle\n", "  - race_info_2020.pickle\n", "  - race_info_2021.pickle\n", "  - race_info_2022.pickle\n", "  - race_info_2024.pickle\n", "  - race_results_2017.pickle\n", "  - race_results_2018.pickle\n", "  - race_results_2019.pickle\n", "  - race_results_2020.pickle\n", "  - race_results_2021.pickle\n", "  - race_results_2022.pickle\n", "  - race_results_2024.pickle\n"]}], "source": ["# 複数年のデータを統合\n", "print(\"=== 複数年データの統合 ===\")\n", "\n", "if len(CONFIG['target_years']) > 1:\n", "    combined_data = {}\n", "    \n", "    # データタイプごとに統合\n", "    data_types = ['race_info', 'race_results', 'horse_info', 'horse_results']\n", "    \n", "    for data_type in data_types:\n", "        dfs_to_combine = []\n", "        \n", "        for year in CONFIG['target_years']:\n", "            if year in race_results and data_type in race_results[year]:\n", "                df = race_results[year][data_type]\n", "                if df is not None and not df.empty:\n", "                    dfs_to_combine.append(df)\n", "        \n", "        if dfs_to_combine:\n", "            combined_df = pd.concat(dfs_to_combine, ignore_index=True)\n", "            combined_data[data_type] = combined_df\n", "            \n", "            print(f\"{data_type}: {len(combined_df):,}行 (統合後)\")\n", "            \n", "            # 統合データの保存\n", "            years_str = '_'.join(CONFIG['target_years'])\n", "            output_path = CONFIG['output_dir'] / f\"{data_type}_{years_str}_combined.pickle\"\n", "            combined_df.to_pickle(output_path)\n", "            print(f\"  保存完了: {output_path}\")\n", "    \n", "    print(\"\\n複数年データの統合完了\")\n", "else:\n", "    print(\"単一年のデータのため、統合処理をスキップします\")\n", "\n", "print(\"\\n=== 全ての処理が完了しました ===\")\n", "print(f\"出力ディレクトリ: {CONFIG['output_dir']}\")\n", "print(\"保存されたファイル:\")\n", "for file_path in CONFIG['output_dir'].glob('*.pickle'):\n", "    print(f\"  - {file_path.name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}