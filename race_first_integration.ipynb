{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# レースデータ優先処理による競馬データ統合\n", "\n", "## 概要\n", "このノートブックでは、以下のモジュールを使用してデータを処理します：\n", "\n", "- `ComprehensiveDataIntegrator`: データ統合の基本機能\n", "- `HorseProcessor`: 馬情報の処理\n", "- `RaceProcessor`: レース情報の処理\n", "\n", "### 処理フロー\n", "1. レースデータを先に処理\n", "2. 馬IDを抽出\n", "3. 抽出した馬IDに基づいて馬データを収集"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 環境設定"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 環境設定完了\n", "📊 pandas version: 2.2.3\n"]}], "source": ["import logging\n", "import sys\n", "from pathlib import Path\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pickle\n", "from tqdm.notebook import tqdm\n", "\n", "# 既存のモジュールをインポート\n", "from module.comprehensive_data_integrator import ComprehensiveDataIntegrator\n", "from module.horse_processor import HorseProcessor, HorseInfoCols\n", "from module.race_data_processor import RaceProcessor\n", "from module.race_first_processor import RaceFirstProcessor\n", "\n", "# ロギングの設定（DEBUGレベルに変更）\n", "logging.basicConfig(\n", "    level=logging.DEBUG,\n", "    format=\"%(asctime)s - %(levelname)s - %(message)s\",\n", "    handlers=[\n", "        logging.StreamHandler(),\n", "        logging.FileHandler('race_processing_debug.log')\n", "    ]\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"✅ 環境設定完了\")\n", "print(f\"📊 pandas version: {pd.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. データ処理の準備"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 22:07:49,430 - INFO - 設定の初期化とバリデーションに成功しました。\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📅 処理対象年度: ['2022', '2023', '2024']\n", "⚙️ 並列処理ワーカー数: 4\n"]}], "source": ["# 処理設定\n", "TARGET_YEARS = [\"2022\", \"2023\", \"2024\"]  # 処理対象年度\n", "MAX_WORKERS = 4  # 並列処理のワーカー数\n", "\n", "# 各プロセッサーの初期化\n", "race_first = RaceFirstProcessor()\n", "horse_processor = HorseProcessor()\n", "race_processor = RaceProcessor()\n", "integrator = ComprehensiveDataIntegrator()\n", "\n", "print(f\"📅 処理対象年度: {TARGET_YEARS}\")\n", "print(f\"⚙️ 並列処理ワーカー数: {MAX_WORKERS}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. レースデータの処理"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 22:07:50,858 - INFO - Processing races for year 2022\n", "2025-05-26 22:07:50,859 - INFO - Found race file: data\\race_data_2022_20250525_201307.pickle\n", "2025-05-26 22:07:50,912 - ERROR - Error loading race file data\\race_data_2022_20250525_201307.pickle: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().\n", "2025-05-26 22:07:50,913 - INFO - Processing races for year 2023\n", "2025-05-26 22:07:50,914 - INFO - Found race file: data\\race_data_2023_20250525_201919.pickle\n", "2025-05-26 22:07:50,964 - ERROR - Error loading race file data\\race_data_2023_20250525_201919.pickle: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().\n", "2025-05-26 22:07:50,964 - INFO - Processing races for year 2024\n", "2025-05-26 22:07:50,965 - INFO - Found race file: data\\race_data_2024_20250525_202538.pickle\n", "2025-05-26 22:07:51,012 - ERROR - Error loading race file data\\race_data_2024_20250525_202538.pickle: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🏇 レースデータの処理を開始...\n", "❌ エラーが発生しました: No race results were processed successfully\n", "詳細なエラー情報:\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_12380\\1676912389.py\", line 4, in <module>\n", "    race_results, horse_ids = race_first.process_races_first(\n", "  File \"f:\\keiba__AI_2025\\module\\race_first_processor.py\", line 98, in process_races_first\n", "    except Exception as e:\n", "ValueError: No race results were processed successfully\n", "\n"]}], "source": ["try:\n", "    # レース情報の処理と馬IDの抽出\n", "    print(\"🏇 レースデータの処理を開始...\")\n", "    race_results, horse_ids = race_first.process_races_first(\n", "        target_years=TARGET_YEARS,\n", "        max_workers=MAX_WORKERS\n", "    )\n", "    \n", "    print(f\"✅ レースデータ処理完了\")\n", "    print(f\"📊 処理したレース数: {len(race_results['race_id'].unique())}\")\n", "    print(f\"🐎 抽出した馬ID数: {len(horse_ids)}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ エラーが発生しました: {str(e)}\")\n", "    # スタックトレースも表示\n", "    import traceback\n", "    print(\"詳細なエラー情報:\")\n", "    print(traceback.format_exc())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. データ構造の確認"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# レースデータファイルの中身を確認\n", "sample_year = \"2022\"\n", "sample_file = race_first._find_latest_race_file(sample_year)\n", "\n", "if sample_file and sample_file.exists():\n", "    with open(sample_file, \"rb\") as f:\n", "        sample_data = pickle.load(f)\n", "    \n", "    if sample_data:\n", "        sample_race_id = next(iter(sample_data))\n", "        print(f\"サンプルレースID: {sample_race_id}\")\n", "        print(f\"データ構造:\")\n", "        print(f\"キー: {list(sample_data[sample_race_id].keys())}\")\n", "        \n", "        if \"result_table\" in sample_data[sample_race_id]:\n", "            result_table = sample_data[sample_race_id][\"result_table\"]\n", "            print(f\"\\nレース結果テーブルの型: {type(result_table)}\")\n", "            print(f\"レース結果テーブルの長さ: {len(result_table)}\")\n", "            \n", "            if result_table:\n", "                print(f\"\\n最初の行のキー: {list(result_table[0].keys()) if isinstance(result_table, list) else 'Not a list'}\")\n", "else:\n", "    print(f\"サンプルファイルが見つかりません: {sample_file}\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 4}