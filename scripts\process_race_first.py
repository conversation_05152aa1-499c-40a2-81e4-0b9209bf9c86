import logging
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))

from module.race_first_processor import RaceFirstProcessor


def main():
    # ロギングの設定
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler("race_processing.log")],
    )
    logger = logging.getLogger(__name__)

    # 処理対象年度の設定
    target_years = ["2022", "2023", "2024"]

    try:
        # プロセッサーの初期化
        processor = RaceFirstProcessor()

        # レース情報の処理と馬IDの抽出
        logger.info(f"Processing races for years: {target_years}")
        race_results, horse_ids = processor.process_races_first(target_years=target_years, max_workers=4)

        logger.info(f"Successfully processed {len(race_results)} races")
        logger.info(f"Extracted {len(horse_ids)} unique horse IDs")

    except Exception as e:
        logger.error(f"Error during processing: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
