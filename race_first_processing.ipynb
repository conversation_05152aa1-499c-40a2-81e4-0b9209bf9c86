{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# レースデータ優先処理ノートブック\n", "\n", "## 概要\n", "このノートブックでは、以下の手順でデータを処理します：\n", "\n", "1. レースデータを先に処理\n", "2. レースデータから馬IDを抽出\n", "3. 抽出した馬IDに基づいて馬データを収集\n", "\n", "### 特徴\n", "- メモリ効率の向上\n", "- 処理速度の改善\n", "- データの整合性確保"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 環境設定"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 環境設定完了\n", "📊 pandas version: 2.2.3\n", "🔢 numpy version: 2.2.5\n"]}], "source": ["import logging\n", "import sys\n", "from pathlib import Path\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pickle\n", "from tqdm.notebook import tqdm\n", "\n", "# モジュールのインポート\n", "from module.race_first_processor import RaceFirstProcessor\n", "\n", "# ロギングの設定\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s - %(levelname)s - %(message)s\"\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"✅ 環境設定完了\")\n", "print(f\"📊 pandas version: {pd.__version__}\")\n", "print(f\"🔢 numpy version: {np.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. データ処理設定"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📅 処理対象年度: ['2022', '2023', '2024']\n", "⚙️ 並列処理ワーカー数: 4\n"]}], "source": ["# 処理設定\n", "TARGET_YEARS = [\"2022\", \"2023\", \"2024\"]  # 処理対象年度\n", "MAX_WORKERS = 4  # 並列処理のワーカー数\n", "\n", "print(f\"📅 処理対象年度: {TARGET_YEARS}\")\n", "print(f\"⚙️ 並列処理ワーカー数: {MAX_WORKERS}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. レースデータの処理"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 21:55:47,328 - INFO - Processing races for year 2022\n", "2025-05-26 21:55:47,329 - WARNING - Race data file not found for year 2022\n", "2025-05-26 21:55:47,330 - INFO - Processing races for year 2023\n", "2025-05-26 21:55:47,331 - WARNING - Race data file not found for year 2023\n", "2025-05-26 21:55:47,331 - INFO - Processing races for year 2024\n", "2025-05-26 21:55:47,332 - WARNING - Race data file not found for year 2024\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🏇 レースデータの処理を開始...\n", "❌ エラーが発生しました: No race results were processed successfully\n"]}], "source": ["# プロセッサーの初期化\n", "processor = RaceFirstProcessor()\n", "\n", "try:\n", "    # レース情報の処理と馬IDの抽出\n", "    print(\"🏇 レースデータの処理を開始...\")\n", "    race_results, horse_ids = processor.process_races_first(\n", "        target_years=TARGET_YEARS,\n", "        max_workers=MAX_WORKERS\n", "    )\n", "    \n", "    print(f\"✅ 処理完了\")\n", "    print(f\"📊 処理したレース数: {len(race_results)}\")\n", "    print(f\"🐎 抽出した馬ID数: {len(horse_ids)}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ エラーが発生しました: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. データ分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# レースデータの基本統計\n", "print(\"📊 レースデータの基本統計\")\n", "print(\"=\" * 40)\n", "print(f\"総レース数: {len(race_results)}\")\n", "print(f\"総出走数: {len(race_results.index)}\")\n", "print(f\"ユニーク馬数: {len(horse_ids)}\")\n", "\n", "# 年度別の統計\n", "yearly_stats = race_results.groupby(\n", "    pd.to_datetime(race_results['race_date']).dt.year\n", ").agg({\n", "    'race_id': 'nunique',\n", "    'horse_id': 'count'\n", "}).rename(columns={\n", "    'race_id': 'レース数',\n", "    'horse_id': '出走数'\n", "})\n", "\n", "print(\"\\n📅 年度別統計\")\n", "print(\"=\" * 40)\n", "display(yearly_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. データ品質チェック"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 重要カラムの欠損値チェック\n", "important_cols = [\n", "    'race_id', 'horse_id', 'race_date', 'race_name',\n", "    'race_course', 'weather', 'ground_state', 'race_type'\n", "]\n", "\n", "null_stats = race_results[important_cols].isnull().sum()\n", "null_percentages = (null_stats / len(race_results)) * 100\n", "\n", "print(\"📋 重要カラムの欠損値状況\")\n", "print(\"=\" * 40)\n", "for col in important_cols:\n", "    print(f\"{col}: {null_percentages[col]:.2f}% ({null_stats[col]} 件)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 次のステップ\n", "\n", "1. 抽出した馬IDを使用して馬データを収集\n", "2. レースデータと馬データの結合\n", "3. 特徴量エンジニアリング\n", "4. モデル学習用データセットの作成"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 4}