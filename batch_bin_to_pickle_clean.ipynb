{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 複数年分のbinファイルをDataFrameに変換しpickle保存するNotebook\n", "\n", "このNotebookは、module/race_data_processor.pyのRaceProcessorを利用して、\n", "複数年分のbinファイルをDataFrameに変換し、outputディレクトリにpickleファイルとして保存します。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "from pathlib import Path\n", "\n", "from module.race_data_processor import RaceProcessor\n", "\n", "import pandas as pd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 設定"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["処理対象年: ['2017', '2018', '2019']\n", "データディレクトリ: f:\\keiba__AI_2025\\data\\html\\race\\race_by_year\n", "出力ディレクトリ: output\n"]}], "source": ["# 処理したい年のリストを指定\n", "years = ['2017', '2018', '2019']  # 必要な年を追加してください\n", "# 単一年の場合: years = ['2024']\n", "# 複数年の場合: years = ['2020', '2021', '2022', '2023', '2024']\n", "\n", "# データディレクトリの設定\n", "base_dir = Path.cwd()  # 現在のディレクトリを基準\n", "data_dir = base_dir / 'data' / 'html' / 'race' / 'race_by_year'\n", "\n", "# 出力ディレクトリの作成\n", "output_dir = Path('output')\n", "output_dir.mkdir(exist_ok=True)\n", "\n", "print(f\"処理対象年: {years}\")\n", "print(f\"データディレクトリ: {data_dir}\")\n", "print(f\"出力ディレクトリ: {output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RaceProcessorのインスタンス化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processor = RaceProcessor()\n", "print(\"RaceProcessorを初期化しました\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 各年のbinファイルをDataFrameに変換し、pickleで保存"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for year in years:\n", "    print(f'\\n{year}年のbinファイルを処理中...')\n", "    \n", "    # binファイルのディレクトリとファイル一覧を取得\n", "    bin_dir = data_dir / year\n", "    if not bin_dir.exists():\n", "        print(f\"  警告: {bin_dir} が存在しません。スキップします。\")\n", "        continue\n", "        \n", "    bin_files = list(bin_dir.glob('*.bin'))\n", "    if not bin_files:\n", "        print(f\"  警告: {bin_dir} にbinファイルが見つかりません。スキップします。\")\n", "        continue\n", "        \n", "    print(f\"  {len(bin_files)}個のbinファイルを発見\")\n", "    \n", "    # 各binファイルを処理\n", "    race_info_list = []\n", "    race_results_list = []\n", "    \n", "    for i, bin_file in enumerate(bin_files, 1):\n", "        if i % 100 == 0:  # 進捗表示\n", "            print(f\"  処理中: {i}/{len(bin_files)}\")\n", "            \n", "        try:\n", "            info_df, results_df = processor.parse_race_html(html_path=bin_file)\n", "            if not info_df.empty:\n", "                race_info_list.append(info_df)\n", "            if not results_df.empty:\n", "                race_results_list.append(results_df)\n", "        except Exception as e:\n", "            print(f\"  エラー: {bin_file.name} の処理中にエラーが発生: {e}\")\n", "            continue\n", "    \n", "    # 年ごとにまとめてDataFrame化\n", "    race_info_df = pd.concat(race_info_list, ignore_index=True) if race_info_list else pd.DataFrame()\n", "    race_results_df = pd.concat(race_results_list, ignore_index=True) if race_results_list else pd.DataFrame()\n", "    \n", "    # DataFrameが空でなければ保存\n", "    if not race_info_df.empty:\n", "        race_info_path = output_dir / f'race_info_{year}.pickle'\n", "        race_info_df.to_pickle(race_info_path)\n", "        print(f\"  ✓ race_info_{year}.pickle を保存 (行数: {len(race_info_df)})\")\n", "    else:\n", "        print(f\"  警告: race_info_{year} のデータが空です\")\n", "        \n", "    if not race_results_df.empty:\n", "        race_results_path = output_dir / f'race_results_{year}.pickle'\n", "        race_results_df.to_pickle(race_results_path)\n", "        print(f\"  ✓ race_results_{year}.pickle を保存 (行数: {len(race_results_df)})\")\n", "    else:\n", "        print(f\"  警告: race_results_{year} のデータが空です\")\n", "    \n", "    print(f'{year}年の処理完了')\n", "\n", "print('\\n全ての年の処理が完了しました。')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 保存されたファイルの確認"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存されたpickleファイルの一覧と基本情報を表示\n", "pickle_files = list(output_dir.glob('*.pickle'))\n", "if pickle_files:\n", "    print(\"保存されたファイル:\")\n", "    for pickle_file in sorted(pickle_files):\n", "        try:\n", "            df = pd.read_pickle(pickle_file)\n", "            print(f\"  {pickle_file.name}: {len(df)}行, {len(df.columns)}列\")\n", "            if len(df) > 0:\n", "                print(f\"    カラム: {list(df.columns)[:5]}{'...' if len(df.columns) > 5 else ''}\")\n", "        except Exception as e:\n", "            print(f\"  {pickle_file.name}: 読み込みエラー - {e}\")\n", "else:\n", "    print(\"保存されたpickleファイルが見つかりません。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## オプション: 馬IDの抽出と保存\n", "\n", "レース結果から馬IDを抽出してCSVファイルとして保存する場合は、以下のセルを実行してください。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 各年の馬IDを抽出して保存\n", "all_horse_ids = set()\n", "horse_ids_by_year = {}\n", "\n", "for year in years:\n", "    # race_results_{year}.pickle からDataFrameを読み込み\n", "    df_path = output_dir / f\"race_results_{year}.pickle\"\n", "    if not df_path.exists():\n", "        print(f\"{df_path} が存在しません。スキップします。\")\n", "        continue\n", "        \n", "    try:\n", "        race_results_df = pd.read_pickle(df_path)\n", "        # horse_idカラムからユニークなIDを抽出\n", "        if 'horse_id' in race_results_df.columns:\n", "            horse_ids = set(race_results_df[\"horse_id\"].dropna().unique())\n", "            horse_ids_by_year[year] = horse_ids\n", "            all_horse_ids.update(horse_ids)\n", "            \n", "            # 年ごとに保存\n", "            horse_ids_df = pd.DataFrame(sorted(horse_ids), columns=[\"horse_id\"])\n", "            horse_ids_path = output_dir / f\"horse_ids_{year}.csv\"\n", "            horse_ids_df.to_csv(horse_ids_path, index=False)\n", "            print(f\"{year}年の馬ID数: {len(horse_ids)} を保存\")\n", "        else:\n", "            print(f\"{year}年のデータにhorse_idカラムが見つかりません\")\n", "    except Exception as e:\n", "        print(f\"{year}年の馬ID抽出中にエラー: {e}\")\n", "\n", "# 全体の馬IDも保存\n", "if all_horse_ids:\n", "    all_horse_ids_df = pd.DataFrame(sorted(all_horse_ids), columns=[\"horse_id\"])\n", "    all_horse_ids_path = output_dir / \"horse_ids_all.csv\"\n", "    all_horse_ids_df.to_csv(all_horse_ids_path, index=False)\n", "    print(f\"全体の馬ID数: {len(all_horse_ids)} を保存\")\n", "else:\n", "    print(\"馬IDが見つかりませんでした\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 4}