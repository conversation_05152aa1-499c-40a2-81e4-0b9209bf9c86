#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
horse_resultsデータに性別情報が含まれていない問題を調査するテストファイル
"""

import pandas as pd
import sys
import os
from pathlib import Path

# moduleディレクトリをパスに追加
sys.path.append('module')

from horse_processor import HorseProcessor
from constants import HorseResultsCols

def test_horse_results_columns():
    """horse_resultsデータの列構造を確認"""
    print("=== horse_resultsデータの列構造確認 ===")

    # HorseProcessorのインスタンスを作成
    processor = HorseProcessor()

    # サンプルデータを取得して列構造を確認
    try:
        print("サンプルデータを取得中...")
        summary = processor.get_horse_results_summary(max_sample_files=5)

        if 'sample_data' in summary and summary['sample_data'] is not None:
            sample_df = summary['sample_data']
        else:
            sample_df = None

        if sample_df is not None and not sample_df.empty:
            print(f"サンプルデータ形状: {sample_df.shape}")
            print(f"列名一覧:")
            for i, col in enumerate(sample_df.columns):
                print(f"  {i+1:2d}. {col}")

            # 性齢列の存在確認
            sex_age_col = HorseResultsCols.SEX_AGE
            print(f"\n性齢列 '{sex_age_col}' の存在確認:")
            if sex_age_col in sample_df.columns:
                print(f"  ✓ '{sex_age_col}' 列が存在します")
                print(f"  サンプル値: {sample_df[sex_age_col].head().tolist()}")
                print(f"  ユニーク値数: {sample_df[sex_age_col].nunique()}")
                print(f"  ユニーク値例: {sample_df[sex_age_col].unique()[:10].tolist()}")
            else:
                print(f"  ✗ '{sex_age_col}' 列が存在しません")

            # 性別関連の列を確認
            sex_related_cols = ['性', '年齢', '性別', 'sex', 'age']
            print(f"\n性別関連列の確認:")
            for col in sex_related_cols:
                if col in sample_df.columns:
                    print(f"  ✓ '{col}' 列が存在します")
                    print(f"    サンプル値: {sample_df[col].head().tolist()}")
                else:
                    print(f"  ✗ '{col}' 列は存在しません")

            # データの先頭5行を表示
            print(f"\nデータの先頭5行:")
            print(sample_df.head())

        else:
            print("サンプルデータが取得できませんでした")

    except Exception as e:
        print(f"エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

def test_html_parsing():
    """HTMLパースの段階で性齢情報が取得されているかを確認"""
    print("\n=== HTMLパース段階での性齢情報確認 ===")

    try:
        from horse_html_parser import HorseHtmlParser

        parser = HorseHtmlParser()

        # HTMLファイルを探す
        html_dir = Path("data/html/horse/horse_by_year")
        if html_dir.exists():
            html_files = list(html_dir.rglob("*.bin"))  # .binファイルを探す
            if html_files:
                # 最初のHTMLファイルをテスト
                test_file = html_files[0]
                print(f"テストファイル: {test_file}")

                # HTMLをパース
                df = parser.parse_horse_results_html(str(test_file))

                if df is not None and not df.empty:
                    print(f"パース結果の形状: {df.shape}")
                    print(f"列名一覧:")
                    for i, col in enumerate(df.columns):
                        print(f"  {i+1:2d}. {col}")

                    # 性齢列の確認
                    sex_age_col = HorseResultsCols.SEX_AGE
                    if sex_age_col in df.columns:
                        print(f"\n'{sex_age_col}' 列の内容:")
                        print(f"  サンプル値: {df[sex_age_col].head().tolist()}")
                        print(f"  データ型: {df[sex_age_col].dtype}")
                    else:
                        print(f"\n'{sex_age_col}' 列が見つかりません")

                else:
                    print("HTMLパースの結果が空です")
            else:
                print("HTMLファイルが見つかりません")
        else:
            print(f"HTMLディレクトリが存在しません: {html_dir}")

    except Exception as e:
        print(f"HTMLパーステストでエラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

def test_constants():
    """定数の確認"""
    print("\n=== 定数確認 ===")

    print(f"HorseResultsCols.SEX_AGE = '{HorseResultsCols.SEX_AGE}'")

    # 他の関連定数も確認
    attrs = [attr for attr in dir(HorseResultsCols) if not attr.startswith('_')]
    print(f"HorseResultsCols の全属性:")
    for attr in attrs:
        value = getattr(HorseResultsCols, attr)
        print(f"  {attr} = '{value}'")

if __name__ == "__main__":
    print("horse_resultsデータの性別情報問題調査開始")
    print("=" * 60)

    # 定数確認
    test_constants()

    # HTMLパース確認
    test_html_parsing()

    # 列構造確認
    test_horse_results_columns()

    print("\n調査完了")
