#!/bin/bash

# エラーが発生した場合に停止
set -e

# 環境変数の読み込み
source .env.production

# 必要なディレクトリの作成
echo "ディレクトリの作成..."
mkdir -p /var/log/keiba_ai
mkdir -p /var/cache/keiba_ai

# 依存関係のインストール
echo "依存関係のインストール..."
pip install -r requirements.txt

# 設定ファイルの配置
echo "設定ファイルの配置..."
cp config/production.yaml /etc/keiba_ai/config.yaml

# データベースのマイグレーション
echo "データベースのマイグレーション..."
python scripts/migrate_db.py

# サービスの再起動
echo "サービスの再起動..."
systemctl restart keiba_ai

echo "デプロイメント完了" 