#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
包括的データ統合の使用例
母父情報を含む包括的な競馬データ統合表の生成例
"""

import logging
import pandas as pd
from comprehensive_data_integrator import ComprehensiveDataIntegrator


def example_basic_integration():
    """
    基本的な統合例
    """
    print("🏇 基本的な包括的データ統合の例")
    print("=" * 50)
    
    # ロギング設定
    logging.basicConfig(level=logging.INFO,
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # インテグレーターを作成
    integrator = ComprehensiveDataIntegrator()
    
    # 2024年のデータで包括的な統合表を生成
    print("\n📊 2024年のデータで包括的統合表を生成中...")
    comprehensive_df = integrator.generate_comprehensive_table(
        year="2024",
        include_race_info=True,      # レース情報を含める
        include_horse_info=True,     # 馬基本情報（血統含む）を含める
        include_past_performance=True,  # 馬過去成績統計を含める
        performance_window_races=[5, 10],  # 直近5戦と10戦の統計
        parallel=True,
        max_workers=4
    )
    
    if not comprehensive_df.empty:
        print(f"\n✅ 統合完了!")
        print(f"   データ件数: {len(comprehensive_df):,}件")
        print(f"   カラム数: {len(comprehensive_df.columns)}個")
        
        # 血統情報の確認
        pedigree_cols = [col for col in comprehensive_df.columns 
                        if any(keyword in col.lower() for keyword in ['father', 'mother'])]
        if pedigree_cols:
            print(f"\n🧬 血統情報カラム ({len(pedigree_cols)}個):")
            for col in pedigree_cols:
                print(f"   - {col}")
        
        # サンプルデータの表示
        print(f"\n📋 サンプルデータ (最初の3行):")
        sample_cols = ['race_id', 'horse_id', '馬名', '着順', 'father_name', 'mother_name', 'mother_father_name']
        available_cols = [col for col in sample_cols if col in comprehensive_df.columns]
        if available_cols:
            print(comprehensive_df[available_cols].head(3).to_string())
        
        # ファイルに保存
        print(f"\n💾 ファイルに保存中...")
        pickle_path, csv_path = integrator.save_comprehensive_table(
            filename_prefix="example_comprehensive_2024",
            year="2024"
        )
        
        if csv_path:
            print(f"✅ CSV保存完了: {csv_path}")
        if pickle_path:
            print(f"✅ Pickle保存完了: {pickle_path}")
            
        return comprehensive_df
    else:
        print("❌ データの生成に失敗しました")
        return pd.DataFrame()


def example_custom_integration():
    """
    カスタム設定での統合例
    """
    print("\n🔧 カスタム設定での統合例")
    print("=" * 50)
    
    integrator = ComprehensiveDataIntegrator()
    
    # レース情報のみを含む軽量版
    print("\n📊 レース情報のみの軽量版を生成中...")
    lightweight_df = integrator.generate_comprehensive_table(
        year="2024",
        include_race_info=True,       # レース情報を含める
        include_horse_info=False,     # 馬基本情報は含めない
        include_past_performance=False,  # 過去成績統計は含めない
        parallel=True,
        max_workers=2
    )
    
    if not lightweight_df.empty:
        print(f"✅ 軽量版生成完了: {len(lightweight_df)}件, {len(lightweight_df.columns)}カラム")
        
        # レース情報カラムの確認
        race_info_cols = [col for col in lightweight_df.columns 
                         if any(keyword in col.lower() for keyword in ['天気', '馬場', '距離', 'コース'])]
        if race_info_cols:
            print(f"🏁 レース情報カラム ({len(race_info_cols)}個):")
            for col in race_info_cols:
                print(f"   - {col}")


def example_performance_focused():
    """
    過去成績重視の統合例
    """
    print("\n📈 過去成績重視の統合例")
    print("=" * 50)
    
    integrator = ComprehensiveDataIntegrator()
    
    # 過去成績統計を詳細に含む版
    print("\n📊 過去成績統計詳細版を生成中...")
    performance_df = integrator.generate_comprehensive_table(
        year="2024",
        include_race_info=True,
        include_horse_info=True,
        include_past_performance=True,
        performance_window_races=[3, 5, 10, 20],  # 複数のウィンドウサイズ
        parallel=True,
        max_workers=4
    )
    
    if not performance_df.empty:
        print(f"✅ 過去成績詳細版生成完了: {len(performance_df)}件, {len(performance_df.columns)}カラム")
        
        # 過去成績統計カラムの確認
        perf_cols = [col for col in performance_df.columns 
                    if any(keyword in col.lower() for keyword in ['last_', 'mean', 'std'])]
        if perf_cols:
            print(f"📊 過去成績統計カラム ({len(perf_cols)}個):")
            for col in sorted(perf_cols)[:10]:  # 最初の10個を表示
                print(f"   - {col}")
            if len(perf_cols) > 10:
                print(f"   ... 他{len(perf_cols) - 10}個")


def example_data_analysis():
    """
    生成されたデータの分析例
    """
    print("\n🔍 データ分析例")
    print("=" * 50)
    
    integrator = ComprehensiveDataIntegrator()
    
    # 基本的な統合データを生成
    df = integrator.generate_comprehensive_table(
        year="2024",
        include_race_info=True,
        include_horse_info=True,
        include_past_performance=True,
        performance_window_races=[5, 10],
        parallel=True,
        max_workers=4
    )
    
    if not df.empty:
        print(f"\n📊 データ分析結果:")
        
        # 基本統計
        print(f"   総レコード数: {len(df):,}件")
        print(f"   ユニークレース数: {df['race_id'].nunique() if 'race_id' in df.columns else 0}")
        print(f"   ユニーク馬数: {df['horse_id'].nunique() if 'horse_id' in df.columns else 0}")
        
        # 血統情報の充実度
        if 'father_name' in df.columns:
            father_coverage = (df['father_name'].notna()).mean() * 100
            print(f"   父馬情報カバー率: {father_coverage:.1f}%")
        
        if 'mother_name' in df.columns:
            mother_coverage = (df['mother_name'].notna()).mean() * 100
            print(f"   母馬情報カバー率: {mother_coverage:.1f}%")
        
        if 'mother_father_name' in df.columns:
            mother_father_coverage = (df['mother_father_name'].notna()).mean() * 100
            print(f"   母父情報カバー率: {mother_father_coverage:.1f}%")
        
        # 過去成績統計の有効性
        perf_cols = [col for col in df.columns if 'last_5' in col and 'mean' in col]
        if perf_cols:
            valid_perf = (df[perf_cols[0]].notna()).mean() * 100
            print(f"   過去成績統計カバー率: {valid_perf:.1f}%")
        
        # データ概要の取得
        summary = integrator.get_data_summary()
        print(f"\n📋 データ構成詳細:")
        for category, columns in summary["data_columns"].items():
            if columns:
                print(f"   {category}: {len(columns)}個")


def main():
    """
    メイン実行関数
    """
    print("🏇 包括的競馬データ統合の使用例")
    print("=" * 60)
    print("母父情報を含む包括的な競馬データ統合表の生成例を実行します")
    
    try:
        # 1. 基本的な統合例
        comprehensive_df = example_basic_integration()
        
        # 2. カスタム設定での統合例
        example_custom_integration()
        
        # 3. 過去成績重視の統合例
        example_performance_focused()
        
        # 4. データ分析例
        if not comprehensive_df.empty:
            example_data_analysis()
        
        print(f"\n🎉 全ての例の実行が完了しました!")
        print(f"生成されたファイルは data/csv/ ディレクトリに保存されています。")
        
    except Exception as e:
        print(f"❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
