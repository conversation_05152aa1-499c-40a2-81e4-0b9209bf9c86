import sys
from module.horse_html_parser import HorseHtmlParser
from module.constants import HorseInfoCols

# テスト対象のファイルパス
html_path = r"F:\keiba__AI_2025\data\html\horse\horse_by_year\2021\2021100016.bin"

parser = HorseHtmlParser()
df = parser.parse_horse_info_html(html_path)

if df is None:
    print("パース失敗またはテーブルなし")
    sys.exit(1)

row = df.iloc[0]
print("父:", row.get(HorseInfoCols.FATHER_NAME), row.get(HorseInfoCols.FATHER_ID))
print("母:", row.get(HorseInfoCols.MOTHER_NAME), row.get(HorseInfoCols.MOTHER_ID))
print("母父:", row.get(HorseInfoCols.MOTHER_FATHER_NAME), row.get(HorseInfoCols.MOTHER_FATHER_ID))
