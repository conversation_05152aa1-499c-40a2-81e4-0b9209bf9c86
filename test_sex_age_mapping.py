#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RaceProcessorの性齢情報抽出機能をテストするスクリプト
"""

import pandas as pd
import sys
import os
from pathlib import Path

# moduleディレクトリをパスに追加
sys.path.append('module')

from race_data_processor import RaceProcessor

def test_sex_age_mapping_extraction():
    """性齢情報マッピング抽出機能のテスト"""
    print("=== 性齢情報マッピング抽出テスト ===")
    
    try:
        # RaceProcessorのインスタンスを作成
        processor = RaceProcessor()
        
        # 2022年のデータから性齢情報を抽出（少数のファイルでテスト）
        print("2022年のレースデータから性齢情報を抽出中...")
        
        # 特定のHTMLファイルを指定してテスト
        test_html_files = [
            "data/html/race/race_by_year/2022/202201010101.bin",
            "data/html/race/race_by_year/2022/202201010201.bin",
            "data/html/race/race_by_year/2022/202201010301.bin"
        ]
        
        # 存在するファイルのみをフィルタ
        existing_files = [f for f in test_html_files if os.path.exists(f)]
        
        if not existing_files:
            print("テスト用のHTMLファイルが見つかりません")
            print("年度指定でテストを実行します...")
            
            # 年度指定でテスト（最初の5ファイルのみ）
            mapping_df = processor.extract_horse_sex_age_mapping(year=2022)
        else:
            print(f"テスト対象ファイル: {existing_files}")
            mapping_df = processor.extract_horse_sex_age_mapping(html_path_list=existing_files)
        
        if mapping_df is not None and not mapping_df.empty:
            print(f"\n性齢情報マッピング抽出成功!")
            print(f"抽出件数: {len(mapping_df)}件")
            print(f"列名: {list(mapping_df.columns)}")
            
            # データの内容を確認
            print(f"\n最初の10件:")
            print(mapping_df.head(10))
            
            # 性別の分布を確認
            if '性' in mapping_df.columns:
                print(f"\n性別分布:")
                print(mapping_df['性'].value_counts())
            
            # 年齢の分布を確認
            if '年齢' in mapping_df.columns:
                print(f"\n年齢分布:")
                print(mapping_df['年齢'].value_counts().sort_index())
            
            # 重複チェック
            if 'horse_id' in mapping_df.columns:
                duplicates = mapping_df['horse_id'].duplicated().sum()
                print(f"\n重複する馬ID数: {duplicates}")
                
                if duplicates > 0:
                    print("重複している馬IDの例:")
                    duplicate_horses = mapping_df[mapping_df['horse_id'].duplicated(keep=False)]
                    print(duplicate_horses.head())
            
            # データの保存
            output_file = "data/processed/horse_sex_age_mapping_2022.pkl"
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            mapping_df.to_pickle(output_file)
            print(f"\n性齢マッピングデータを保存しました: {output_file}")
            
        else:
            print("性齢情報マッピングの抽出に失敗しました")
            
    except Exception as e:
        print(f"エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

def test_single_race_parsing():
    """単一レースファイルのパース結果を詳細確認"""
    print("\n=== 単一レースファイル詳細確認 ===")
    
    try:
        processor = RaceProcessor()
        
        # テストファイル
        test_file = "data/html/race/race_by_year/2022/202201010101.bin"
        
        if not os.path.exists(test_file):
            print(f"テストファイルが存在しません: {test_file}")
            return
        
        print(f"テストファイル: {test_file}")
        
        # レース結果をパース
        race_info_df, results_df = processor.parse_race_html(test_file)
        
        print(f"\nレース情報DataFrame:")
        print(f"形状: {race_info_df.shape}")
        if not race_info_df.empty:
            print(race_info_df.head())
        
        print(f"\nレース結果DataFrame:")
        print(f"形状: {results_df.shape}")
        print(f"列名: {list(results_df.columns)}")
        
        if not results_df.empty:
            print(f"最初の5行:")
            print(results_df.head())
            
            # 性齢列の確認
            from constants import ResultsCols
            if ResultsCols.SEX_AGE in results_df.columns:
                print(f"\n性齢列の内容:")
                print(f"列名: {ResultsCols.SEX_AGE}")
                print(f"サンプル値: {results_df[ResultsCols.SEX_AGE].head().tolist()}")
                print(f"ユニーク値: {results_df[ResultsCols.SEX_AGE].unique()}")
            else:
                print(f"\n性齢列が見つかりません")
                print(f"利用可能な列: {list(results_df.columns)}")
        
    except Exception as e:
        print(f"エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("RaceProcessorの性齢情報抽出機能テスト開始")
    print("=" * 60)
    
    # 単一レースファイルの詳細確認
    test_single_race_parsing()
    
    # 性齢情報マッピング抽出テスト
    test_sex_age_mapping_extraction()
    
    print("\nテスト完了")
