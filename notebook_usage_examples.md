# production_race_horse_integration.ipynb 使用例

## 🐎 新しい馬データ収集機能の使い方

### 1. 全年度一括処理（推奨）

```python
# データ生成設定
GENERATE_DATA = True
GENERATE_RACE_DATA = True
GENERATE_HORSE_DATA = True
TARGET_YEARS = [2022, 2023, 2024]  # レースデータ用
MAX_FILES_PER_YEAR = None

# 馬過去成績データ生成方法の選択
HORSE_DATA_METHOD = 'all_years'  # 新方式
ALL_YEARS_MAX_FILES = 5000  # 5,000ファイル処理
ALL_YEARS_RANGE = (2020, 2024)  # 2020-2024年
```

**期待される出力:**
```
🐎 馬過去成績データの生成
   📊 全年度一括処理モード (2020-2024年)
   📝 最大ファイル数: 5,000件
   ✅ 全年度馬過去成績データ保存: data/horse_results_all_2020_2024_20250525_180000.pickle (XX,XXX件)
   📊 年度別件数: {2020: 1234, 2021: 1456, 2022: 1678, 2023: 890, 2024: 234}
```

### 2. 年度別処理（従来方式）

```python
# 馬過去成績データ生成方法の選択
HORSE_DATA_METHOD = 'by_year'  # 従来方式
TARGET_YEARS = [2022, 2023, 2024]
MAX_FILES_PER_YEAR = 1000  # 年度あたり1,000ファイル
```

**期待される出力:**
```
🐎 馬過去成績データの生成
   📅 年度別処理モード
   2022年の馬過去成績データを処理中...
   ✅ 2022年馬過去成績データ保存: data/horse_results_2022_20250525_180000.pickle (X,XXX件)
   2023年の馬過去成績データを処理中...
   ✅ 2023年馬過去成績データ保存: data/horse_results_2023_20250525_180001.pickle (X,XXX件)
```

### 3. 大量データ処理

```python
# 全データ処理（注意：時間がかかります）
HORSE_DATA_METHOD = 'all_years'
ALL_YEARS_MAX_FILES = None  # 制限なし（44,000+ファイル）
ALL_YEARS_RANGE = (1996, 2024)  # 全年度
```

### 4. テスト用設定

```python
# 少量データでテスト
HORSE_DATA_METHOD = 'all_years'
ALL_YEARS_MAX_FILES = 100  # 100ファイルのみ
ALL_YEARS_RANGE = (2022, 2022)  # 2022年のみ
```

## 🔧 設定パラメータ詳細

| パラメータ | 説明 | 推奨値 |
|-----------|------|--------|
| `HORSE_DATA_METHOD` | 処理方式 | `'all_years'` |
| `ALL_YEARS_MAX_FILES` | 最大ファイル数 | `5000` |
| `ALL_YEARS_RANGE` | 年度範囲 | `(2020, 2024)` |
| `TARGET_YEARS` | レースデータ年度 | `[2022, 2023, 2024]` |

## 📊 データ量の目安

| 設定 | 処理ファイル数 | 予想レコード数 | 処理時間 |
|------|---------------|---------------|----------|
| テスト | 100件 | ~500件 | 1-2分 |
| 標準 | 5,000件 | ~25,000件 | 10-20分 |
| 大量 | 44,000+件 | ~220,000件 | 2-4時間 |

## ⚠️ 注意点

1. **メモリ使用量**: 大量データ処理時は16GB以上のRAM推奨
2. **処理時間**: 全データ処理は数時間かかる場合があります
3. **ディスク容量**: 生成されるpickleファイルは数GB になる可能性があります
4. **テスト実行**: 初回は少量データでテストしてください

## 🎯 推奨ワークフロー

1. **テスト実行**: `ALL_YEARS_MAX_FILES = 100` でテスト
2. **標準実行**: `ALL_YEARS_MAX_FILES = 5000` で本格処理
3. **データ確認**: 生成されたデータの品質を確認
4. **必要に応じて**: 全データ処理（`ALL_YEARS_MAX_FILES = None`）

これで、年代関係なく馬データを効率的に収集できます！🐎✨
