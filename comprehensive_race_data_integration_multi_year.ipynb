{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 包括的競馬データ統合ノートブック（複数年度対応）\n", "\n", "## 概要\n", "母父情報を含む包括的な競馬データ統合表を生成するノートブックです。\n", "**複数年度のデータを同時に処理できます。**\n", "\n", "### 主な機能\n", "- **複数年度対応**: 複数の年度を指定して一括処理\n", "- **レース情報**: 天気、馬場状態、距離、コース等\n", "- **馬基本情報**: 父馬、母馬、**母父**、調教師、馬主等\n", "- **過去成績統計**: 直近N戦の平均着順、人気、オッズ等\n", "- **柔軟な設定**: 含める情報を選択可能\n", "- **高速処理**: 並列処理対応\n", "\n", "### 生成されるデータ構造\n", "```\n", "ベースデータ（レース結果）\n", "├── レース情報（天気、距離、馬場状態等）\n", "├── 馬基本情報（父馬、母馬、母父、調教師、馬主等）\n", "└── 馬過去成績統計（直近N戦の平均着順、人気、オッズ等）\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 環境設定とライブラリのインポート"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ ライブラリのインポート完了\n", "📊 pandas version: 2.2.3\n", "🔢 numpy version: 2.2.5\n"]}], "source": ["# 必要なライブラリのインポート\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import logging\n", "import os\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 日本語フォント設定\n", "plt.rcParams['font.family'] = 'DejaVu Sans'\n", "sns.set_style(\"whitegrid\")\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "\n", "# ロギング設定\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "print(\"✅ ライブラリのインポート完了\")\n", "print(f\"📊 pandas version: {pd.__version__}\")\n", "print(f\"🔢 numpy version: {np.__version__}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 包括的データ統合モジュールのインポート完了\n", "🏇 ComprehensiveDataIntegrator クラス利用可能\n", "🐎 母父情報を含む血統データ処理対応\n"]}], "source": ["# 包括的データ統合クラスのインポート\n", "from module.comprehensive_data_integrator import ComprehensiveDataIntegrator\n", "from module.horse_processor import HorseProcessor, HorseInfoCols\n", "from module.race_data_processor import RaceProcessor\n", "\n", "print(\"✅ 包括的データ統合モジュールのインポート完了\")\n", "print(\"🏇 ComprehensiveDataIntegrator クラス利用可能\")\n", "print(\"🐎 母父情報を含む血統データ処理対応\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 複数年度対応の包括的データ統合"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.0 データ結合機能の詳細\n", "\n", "#### 🔗 実装されている結合機能\n", "1. **レース情報結合**: `race_id`をキーとしてレース詳細情報を統合\n", "2. **馬基本情報結合**: `horse_id`をキーとして血統・調教師・馬主情報を統合\n", "3. **過去成績統計結合**: 馬の過去成績から統計値を計算して統合\n", "4. **複数年度結合**: 各年度データを`pd.concat()`で縦結合\n", "\n", "#### 🛠️ 結合オプション\n", "- **結合方式**: `left join`（レース結果をベースに保持）\n", "- **重複カラム処理**: サフィックス付与で回避\n", "- **データ型統一**: 結合前に型変換を実行\n", "- **エラーハンドリング**: 結合失敗時の安全な処理"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 データ結合品質チェック機能\n", "\n", "データ結合の品質を確認するための機能を追加します。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ データ結合品質チェック機能を定義しました\n"]}], "source": ["def check_data_merge_quality(df, merge_keys=['race_id', 'horse_id']):\n", "    \"\"\"\n", "    データ結合の品質をチェックする関数\n", "    \n", "    Parameters\n", "    ----------\n", "    df : pd.DataFrame\n", "        結合後のデータフレーム\n", "    merge_keys : list\n", "        結合キーのリスト\n", "    \n", "    Returns\n", "    -------\n", "    dict\n", "        品質チェック結果\n", "    \"\"\"\n", "    quality_report = {}\n", "    \n", "    print(\"🔍 データ結合品質チェック\")\n", "    print(\"=\" * 40)\n", "    \n", "    # 1. 基本統計\n", "    quality_report['total_records'] = len(df)\n", "    quality_report['total_columns'] = len(df.columns)\n", "    \n", "    print(f\"📊 総レコード数: {quality_report['total_records']:,}\")\n", "    print(f\"📋 総カラム数: {quality_report['total_columns']}\")\n", "    \n", "    # 2. 結合キーの品質チェック\n", "    for key in merge_keys:\n", "        if key in df.columns:\n", "            null_count = df[key].isnull().sum()\n", "            null_rate = (null_count / len(df)) * 100\n", "            unique_count = df[key].nunique()\n", "            \n", "            quality_report[f'{key}_null_count'] = null_count\n", "            quality_report[f'{key}_null_rate'] = null_rate\n", "            quality_report[f'{key}_unique_count'] = unique_count\n", "            \n", "            print(f\"\\n🔑 {key}:\")\n", "            print(f\"   欠損値: {null_count:,}件 ({null_rate:.2f}%)\")\n", "            print(f\"   ユニーク値: {unique_count:,}件\")\n", "    \n", "    # 3. 重要カラムの欠損率チェック\n", "    important_cols = ['馬名', 'father_name', 'mother_name', 'mother_father_name', \n", "                     'weather', 'ground_state', 'course_len']\n", "    \n", "    print(\"\\n📈 重要カラムの欠損率:\")\n", "    for col in important_cols:\n", "        if col in df.columns:\n", "            null_rate = (df[col].isnull().sum() / len(df)) * 100\n", "            quality_report[f'{col}_null_rate'] = null_rate\n", "            print(f\"   {col}: {null_rate:.1f}%\")\n", "    \n", "    # 4. データ型の一貫性チェック\n", "    print(\"\\n🔧 データ型チェック:\")\n", "    numeric_cols = ['着順', '人気', '単勝', 'course_len']\n", "    for col in numeric_cols:\n", "        if col in df.columns:\n", "            try:\n", "                pd.to_numeric(df[col], errors='coerce')\n", "                print(f\"   ✅ {col}: 数値型変換可能\")\n", "            except:\n", "                print(f\"   ❌ {col}: 数値型変換エラー\")\n", "    \n", "    return quality_report\n", "\n", "print(\"✅ データ結合品質チェック機能を定義しました\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 設定（複数年度指定）"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 20:21:16,150 - INFO - 設定の初期化とバリデーションに成功しました。\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🏇 包括的競馬データ統合を開始します（複数年度対応）\n", "============================================================\n", "📅 対象年度: ['2023', '2024'] (2年度)\n", "📊 過去成績ウィンドウ: [5, 10]\n", "⚙️ 並列処理ワーカー数: 4\n", "\n", "🎯 処理予定: 2年度のデータを統合します\n"]}], "source": ["# 包括的データ統合の実行\n", "print(\"🏇 包括的競馬データ統合を開始します（複数年度対応）\")\n", "print(\"=\" * 60)\n", "\n", "# インテグレーターを作成\n", "integrator = ComprehensiveDataIntegrator()\n", "\n", "# 設定（複数年度対応）\n", "# 以下から選択してください：\n", "\n", "# 🔹 単一年度の場合\n", "# TARGET_YEARS = [\"2024\"]\n", "\n", "# 🔹 複数年度の場合（推奨）\n", "TARGET_YEARS = [\"2023\", \"2024\"]\n", "\n", "# 🔹 より多くの年度の場合\n", "# TARGET_YEARS = [\"2020\", \"2021\", \"2022\", \"2023\", \"2024\"]\n", "\n", "# 🔹 特定の年度のみ\n", "# TARGET_YEARS = [\"2022\", \"2024\"]\n", "\n", "PERFORMANCE_WINDOWS = [5, 10]  # 過去成績の集計対象レース数\n", "MAX_WORKERS = 4  # 　６並列処理のワーカー数\n", "\n", "print(f\"📅 対象年度: {TARGET_YEARS} ({len(TARGET_YEARS)}年度)\")\n", "print(f\"📊 過去成績ウィンドウ: {PERFORMANCE_WINDOWS}\")\n", "print(f\"⚙️ 並列処理ワーカー数: {MAX_WORKERS}\")\n", "print(f\"\\n🎯 処理予定: {len(TARGET_YEARS)}年度のデータを統合します\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 複数年度データの統合実行"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 20:21:33,815 - INFO - 包括的な表形式データの生成を開始します\n", "2025-05-26 20:21:33,816 - INFO - 1. レース情報とレース結果を取得中...\n", "2025-05-26 20:21:33,818 - INFO - 処理対象のファイル数: 240\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 包括的データ統合を実行中...\n", "含める情報:\n", "  ✅ レース情報（天気、馬場状態、距離等）\n", "  ✅ 馬基本情報（血統、調教師、馬主等）\n", "  ✅ 馬過去成績統計（直近N戦の統計）\n", "  🎯 母父情報も含む！\n", "\n", "📅 [1/2] 2023年のデータを処理中...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9816f6b5a29c4d3cb329cabeec4fcdc8", "version_major": 2, "version_minor": 0}, "text/plain": ["HTML解析 (並列):   0%|          | 0/240 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-26 20:21:34,276 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010102, 形状: (16, 21)\n", "2025-05-26 20:21:34,318 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010101, 形状: (16, 21)\n", "2025-05-26 20:21:34,448 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010104, 形状: (16, 21)\n", "2025-05-26 20:21:34,556 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010103, 形状: (16, 21)\n", "2025-05-26 20:21:34,947 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010107, 形状: (16, 21)\n", "2025-05-26 20:21:35,075 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010106, 形状: (18, 21)\n", "2025-05-26 20:21:35,185 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010105, 形状: (16, 21)\n", "2025-05-26 20:21:35,305 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010108, 形状: (16, 21)\n", "2025-05-26 20:21:35,574 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010109, 形状: (16, 21)\n", "2025-05-26 20:21:35,679 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010110, 形状: (15, 21)\n", "2025-05-26 20:21:35,880 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010111, 形状: (18, 21)\n", "2025-05-26 20:21:36,001 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010112, 形状: (16, 21)\n", "2025-05-26 20:21:36,271 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010201, 形状: (16, 21)\n", "2025-05-26 20:21:36,374 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010202, 形状: (16, 21)\n", "2025-05-26 20:21:36,510 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010203, 形状: (16, 21)\n", "2025-05-26 20:21:36,655 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010204, 形状: (16, 21)\n", "2025-05-26 20:21:36,970 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010207, 形状: (12, 21)\n", "2025-05-26 20:21:37,089 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010205, 形状: (16, 21)\n", "2025-05-26 20:21:37,236 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010206, 形状: (18, 21)\n", "2025-05-26 20:21:37,345 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010208, 形状: (14, 21)\n", "2025-05-26 20:21:37,569 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010210, 形状: (15, 21)\n", "2025-05-26 20:21:37,751 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010209, 形状: (16, 21)\n", "2025-05-26 20:21:37,849 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010211, 形状: (16, 21)\n", "2025-05-26 20:21:37,995 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010212, 形状: (16, 21)\n", "2025-05-26 20:21:38,217 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010301, 形状: (16, 21)\n", "2025-05-26 20:21:38,348 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010302, 形状: (16, 21)\n", "2025-05-26 20:21:38,470 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010303, 形状: (16, 21)\n", "2025-05-26 20:21:38,715 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010304, 形状: (16, 21)\n", "2025-05-26 20:21:38,927 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010305, 形状: (18, 21)\n", "2025-05-26 20:21:39,024 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010306, 形状: (13, 21)\n", "2025-05-26 20:21:39,174 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010307, 形状: (16, 21)\n", "2025-05-26 20:21:39,382 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010308, 形状: (16, 21)\n", "2025-05-26 20:21:39,655 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010309, 形状: (14, 21)\n", "2025-05-26 20:21:39,766 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010310, 形状: (16, 21)\n", "2025-05-26 20:21:39,879 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010311, 形状: (13, 21)\n", "2025-05-26 20:21:40,052 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010312, 形状: (10, 21)\n", "2025-05-26 20:21:40,247 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010401, 形状: (16, 21)\n", "2025-05-26 20:21:40,350 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010402, 形状: (16, 21)\n", "2025-05-26 20:21:40,471 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010403, 形状: (16, 21)\n", "2025-05-26 20:21:40,774 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010404, 形状: (16, 21)\n", "2025-05-26 20:21:40,949 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010406, 形状: (16, 21)\n", "2025-05-26 20:21:41,094 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010405, 形状: (18, 21)\n", "2025-05-26 20:21:41,212 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010407, 形状: (18, 21)\n", "2025-05-26 20:21:41,436 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010408, 形状: (16, 21)\n", "2025-05-26 20:21:41,625 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010409, 形状: (14, 21)\n", "2025-05-26 20:21:41,804 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010410, 形状: (16, 21)\n", "2025-05-26 20:21:41,931 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010411, 形状: (16, 21)\n", "2025-05-26 20:21:42,187 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010412, 形状: (16, 21)\n", "2025-05-26 20:21:42,284 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010501, 形状: (16, 21)\n", "2025-05-26 20:21:42,436 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010502, 形状: (16, 21)\n", "2025-05-26 20:21:42,634 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010503, 形状: (16, 21)\n", "2025-05-26 20:21:42,788 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010504, 形状: (9, 21)\n", "2025-05-26 20:21:42,958 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010505, 形状: (16, 21)\n", "2025-05-26 20:21:43,071 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010507, 形状: (11, 21)\n", "2025-05-26 20:21:43,222 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010506, 形状: (17, 21)\n", "2025-05-26 20:21:43,408 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010508, 形状: (14, 21)\n", "2025-05-26 20:21:43,635 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010509, 形状: (16, 21)\n", "2025-05-26 20:21:43,788 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010510, 形状: (16, 21)\n", "2025-05-26 20:21:43,911 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010511, 形状: (16, 21)\n", "2025-05-26 20:21:44,203 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010512, 形状: (16, 21)\n", "2025-05-26 20:21:44,325 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010601, 形状: (16, 21)\n", "2025-05-26 20:21:44,563 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010602, 形状: (16, 21)\n", "2025-05-26 20:21:44,690 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010603, 形状: (16, 21)\n", "2025-05-26 20:21:44,843 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010604, 形状: (11, 21)\n", "2025-05-26 20:21:44,985 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010605, 形状: (18, 21)\n", "2025-05-26 20:21:45,134 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010606, 形状: (16, 21)\n", "2025-05-26 20:21:45,222 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010607, 形状: (13, 21)\n", "2025-05-26 20:21:45,609 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010608, 形状: (16, 21)\n", "2025-05-26 20:21:45,748 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010609, 形状: (16, 21)\n", "2025-05-26 20:21:45,890 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010610, 形状: (16, 21)\n", "2025-05-26 20:21:45,991 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010611, 形状: (16, 21)\n", "2025-05-26 20:21:46,266 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010612, 形状: (16, 21)\n", "2025-05-26 20:21:46,407 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010701, 形状: (16, 21)\n", "2025-05-26 20:21:46,658 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010702, 形状: (16, 21)\n", "2025-05-26 20:21:46,758 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010703, 形状: (16, 21)\n", "2025-05-26 20:21:46,973 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010704, 形状: (11, 21)\n", "2025-05-26 20:21:47,112 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010705, 形状: (16, 21)\n", "2025-05-26 20:21:47,201 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010707, 形状: (13, 21)\n", "2025-05-26 20:21:47,303 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010706, 形状: (16, 21)\n", "2025-05-26 20:21:47,622 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010708, 形状: (14, 21)\n", "2025-05-26 20:21:47,701 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010709, 形状: (12, 21)\n", "2025-05-26 20:21:47,841 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010710, 形状: (16, 21)\n", "2025-05-26 20:21:47,934 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010711, 形状: (14, 21)\n", "2025-05-26 20:21:48,201 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010801, 形状: (16, 21)\n", "2025-05-26 20:21:48,405 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010802, 形状: (16, 21)\n", "2025-05-26 20:21:48,500 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010712, 形状: (16, 21)\n", "2025-05-26 20:21:48,622 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010803, 形状: (16, 21)\n", "2025-05-26 20:21:48,824 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010806, 形状: (6, 21)\n", "2025-05-26 20:21:48,953 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010804, 形状: (16, 21)\n", "2025-05-26 20:21:49,086 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010805, 形状: (18, 21)\n", "2025-05-26 20:21:49,219 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010807, 形状: (16, 21)\n", "2025-05-26 20:21:49,586 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010809, 形状: (10, 21)\n", "2025-05-26 20:21:49,741 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010810, 形状: (13, 21)\n", "2025-05-26 20:21:49,867 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010808, 形状: (16, 21)\n", "2025-05-26 20:21:50,008 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010811, 形状: (17, 21)\n", "2025-05-26 20:21:50,251 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010812, 形状: (16, 21)\n", "2025-05-26 20:21:50,470 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010901, 形状: (16, 21)\n", "2025-05-26 20:21:50,606 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010903, 形状: (15, 21)\n", "2025-05-26 20:21:50,705 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010902, 形状: (16, 21)\n", "2025-05-26 20:21:50,932 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010904, 形状: (16, 21)\n", "2025-05-26 20:21:51,093 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010905, 形状: (18, 21)\n", "2025-05-26 20:21:51,167 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010907, 形状: (11, 21)\n", "2025-05-26 20:21:51,342 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010906, 形状: (16, 21)\n", "2025-05-26 20:21:51,561 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010908, 形状: (14, 21)\n", "2025-05-26 20:21:51,648 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010909, 形状: (11, 21)\n", "2025-05-26 20:21:51,793 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010911, 形状: (18, 21)\n", "2025-05-26 20:21:51,898 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010910, 形状: (15, 21)\n", "2025-05-26 20:21:52,221 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010912, 形状: (14, 21)\n", "2025-05-26 20:21:52,344 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010101, 形状: (16, 21)\n", "2025-05-26 20:21:52,497 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010102, 形状: (16, 21)\n", "2025-05-26 20:21:52,631 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010103, 形状: (16, 21)\n", "2025-05-26 20:21:52,817 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010104, 形状: (16, 21)\n", "2025-05-26 20:21:52,960 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010105, 形状: (18, 21)\n", "2025-05-26 20:21:53,221 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010107, 形状: (9, 21)\n", "2025-05-26 20:21:53,239 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010106, 形状: (16, 21)\n", "2025-05-26 20:21:53,458 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010108, 形状: (16, 21)\n", "2025-05-26 20:21:53,569 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010109, 形状: (11, 21)\n", "2025-05-26 20:21:53,742 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010111, 形状: (16, 21)\n", "2025-05-26 20:21:53,855 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010110, 形状: (16, 21)\n", "2025-05-26 20:21:54,161 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010112, 形状: (16, 21)\n", "2025-05-26 20:21:54,279 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010201, 形状: (16, 21)\n", "2025-05-26 20:21:54,479 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010203, 形状: (16, 21)\n", "2025-05-26 20:21:54,588 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010202, 形状: (16, 21)\n", "2025-05-26 20:21:54,780 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010204, 形状: (12, 21)\n", "2025-05-26 20:21:54,938 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010205, 形状: (18, 21)\n", "2025-05-26 20:21:55,180 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010207, 形状: (16, 21)\n", "2025-05-26 20:21:55,324 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010206, 形状: (18, 21)\n", "2025-05-26 20:21:55,514 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010208, 形状: (16, 21)\n", "2025-05-26 20:21:55,669 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010209, 形状: (16, 21)\n", "2025-05-26 20:21:55,861 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010210, 形状: (18, 21)\n", "2025-05-26 20:21:56,083 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010211, 形状: (17, 21)\n", "2025-05-26 20:21:56,229 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010212, 形状: (16, 21)\n", "2025-05-26 20:21:56,352 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010301, 形状: (16, 21)\n", "2025-05-26 20:21:56,543 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010302, 形状: (16, 21)\n", "2025-05-26 20:21:56,696 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010303, 形状: (16, 21)\n", "2025-05-26 20:21:56,929 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010304, 形状: (16, 21)\n", "2025-05-26 20:21:57,075 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010305, 形状: (18, 21)\n", "2025-05-26 20:21:57,138 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010306, 形状: (7, 21)\n", "2025-05-26 20:21:57,321 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010308, 形状: (11, 21)\n", "2025-05-26 20:21:57,425 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010307, 形状: (16, 21)\n", "2025-05-26 20:21:57,557 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010309, 形状: (13, 21)\n", "2025-05-26 20:21:57,721 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010310, 形状: (16, 21)\n", "2025-05-26 20:21:58,074 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010312, 形状: (16, 21)\n", "2025-05-26 20:21:58,184 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010311, 形状: (16, 21)\n", "2025-05-26 20:21:58,341 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010401, 形状: (16, 21)\n", "2025-05-26 20:21:58,491 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010402, 形状: (16, 21)\n", "2025-05-26 20:21:58,632 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010404, 形状: (12, 21)\n", "2025-05-26 20:21:58,884 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010403, 形状: (16, 21)\n", "2025-05-26 20:21:59,040 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010405, 形状: (16, 21)\n", "2025-05-26 20:21:59,195 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010406, 形状: (18, 21)\n", "2025-05-26 20:21:59,349 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010407, 形状: (16, 21)\n", "2025-05-26 20:21:59,505 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010408, 形状: (16, 21)\n", "2025-05-26 20:21:59,628 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010409, 形状: (16, 21)\n", "2025-05-26 20:21:59,872 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010410, 形状: (16, 21)\n", "2025-05-26 20:22:00,076 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010411, 形状: (18, 21)\n", "2025-05-26 20:22:00,356 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010501, 形状: (16, 21)\n", "2025-05-26 20:22:00,376 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010412, 形状: (18, 21)\n", "2025-05-26 20:22:00,557 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010502, 形状: (16, 21)\n", "2025-05-26 20:22:00,814 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010503, 形状: (16, 21)\n", "2025-05-26 20:22:00,989 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010504, 形状: (15, 21)\n", "2025-05-26 20:22:01,129 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010505, 形状: (16, 21)\n", "2025-05-26 20:22:01,289 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010506, 形状: (16, 21)\n", "2025-05-26 20:22:01,468 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010507, 形状: (18, 21)\n", "2025-05-26 20:22:01,608 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010508, 形状: (16, 21)\n", "2025-05-26 20:22:01,853 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010509, 形状: (16, 21)\n", "2025-05-26 20:22:01,974 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010510, 形状: (16, 21)\n", "2025-05-26 20:22:02,168 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010511, 形状: (15, 21)\n", "2025-05-26 20:22:02,318 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010512, 形状: (14, 21)\n", "2025-05-26 20:22:02,483 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010602, 形状: (11, 21)\n", "2025-05-26 20:22:02,617 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010601, 形状: (16, 21)\n", "2025-05-26 20:22:02,913 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010603, 形状: (16, 21)\n", "2025-05-26 20:22:03,073 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010604, 形状: (16, 21)\n", "2025-05-26 20:22:03,270 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010605, 形状: (18, 21)\n", "2025-05-26 20:22:03,448 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010606, 形状: (16, 21)\n", "2025-05-26 20:22:03,606 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010607, 形状: (12, 21)\n", "2025-05-26 20:22:03,867 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010608, 形状: (16, 21)\n", "2025-05-26 20:22:04,096 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010609, 形状: (16, 21)\n", "2025-05-26 20:22:04,199 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010610, 形状: (12, 21)\n", "2025-05-26 20:22:04,336 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010611, 形状: (16, 21)\n", "2025-05-26 20:22:04,477 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010612, 形状: (13, 21)\n", "2025-05-26 20:22:04,802 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010701, 形状: (16, 21)\n", "2025-05-26 20:22:04,934 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010702, 形状: (13, 21)\n", "2025-05-26 20:22:05,088 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010703, 形状: (16, 21)\n", "2025-05-26 20:22:05,177 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010704, 形状: (10, 21)\n", "2025-05-26 20:22:05,371 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010707, 形状: (10, 21)\n", "2025-05-26 20:22:05,431 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010706, 形状: (10, 21)\n", "2025-05-26 20:22:05,560 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010705, 形状: (18, 21)\n", "2025-05-26 20:22:05,874 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010708, 形状: (16, 21)\n", "2025-05-26 20:22:06,052 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010709, 形状: (11, 21)\n", "2025-05-26 20:22:06,193 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010711, 形状: (16, 21)\n", "2025-05-26 20:22:06,291 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010710, 形状: (16, 21)\n", "2025-05-26 20:22:06,432 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010712, 形状: (16, 21)\n", "2025-05-26 20:22:06,633 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010801, 形状: (16, 21)\n", "2025-05-26 20:22:06,905 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010802, 形状: (16, 21)\n", "2025-05-26 20:22:07,042 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010803, 形状: (16, 21)\n", "2025-05-26 20:22:07,157 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010804, 形状: (16, 21)\n", "2025-05-26 20:22:07,415 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010805, 形状: (18, 21)\n", "2025-05-26 20:22:07,557 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010806, 形状: (16, 21)\n", "2025-05-26 20:22:07,759 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010807, 形状: (15, 21)\n", "2025-05-26 20:22:07,873 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010808, 形状: (16, 21)\n", "2025-05-26 20:22:08,037 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010809, 形状: (10, 21)\n", "2025-05-26 20:22:08,203 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010810, 形状: (13, 21)\n", "2025-05-26 20:22:08,327 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010812, 形状: (12, 21)\n", "2025-05-26 20:22:08,427 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010811, 形状: (14, 21)\n", "2025-05-26 20:22:08,568 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010901, 形状: (11, 21)\n", "2025-05-26 20:22:08,850 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010903, 形状: (11, 21)\n", "2025-05-26 20:22:08,962 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010902, 形状: (16, 21)\n", "2025-05-26 20:22:09,026 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010904, 形状: (8, 21)\n", "2025-05-26 20:22:09,236 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010905, 形状: (16, 21)\n", "2025-05-26 20:22:09,467 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010906, 形状: (16, 21)\n", "2025-05-26 20:22:09,572 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010908, 形状: (13, 21)\n", "2025-05-26 20:22:09,769 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010907, 形状: (16, 21)\n", "2025-05-26 20:22:09,921 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010909, 形状: (9, 21)\n", "2025-05-26 20:22:10,124 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010910, 形状: (14, 21)\n", "2025-05-26 20:22:10,219 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010912, 形状: (13, 21)\n", "2025-05-26 20:22:10,371 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010911, 形状: (16, 21)\n", "2025-05-26 20:22:10,492 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010101, 形状: (12, 21)\n", "2025-05-26 20:22:10,809 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010103, 形状: (13, 21)\n", "2025-05-26 20:22:10,945 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010102, 形状: (17, 21)\n", "2025-05-26 20:22:11,005 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010105, 形状: (7, 21)\n", "2025-05-26 20:22:11,086 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010104, 形状: (10, 21)\n", "2025-05-26 20:22:11,382 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010109, 形状: (12, 21)\n", "2025-05-26 20:22:11,516 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010108, 形状: (16, 21)\n", "2025-05-26 20:22:11,610 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010107, 形状: (16, 21)\n", "2025-05-26 20:22:11,838 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010106, 形状: (18, 21)\n", "2025-05-26 20:22:12,153 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010110, 形状: (16, 21)\n", "2025-05-26 20:22:12,277 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010112, 形状: (16, 21)\n", "2025-05-26 20:22:12,363 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010201, 形状: (13, 21)\n", "2025-05-26 20:22:12,529 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010111, 形状: (18, 21)\n", "2025-05-26 20:22:12,831 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010204, 形状: (12, 21)\n", "2025-05-26 20:22:12,940 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010203, 形状: (14, 21)\n", "2025-05-26 20:22:13,089 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010202, 形状: (17, 21)\n", "2025-05-26 20:22:13,258 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010205, 形状: (18, 21)\n", "2025-05-26 20:22:13,465 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010207, 形状: (14, 21)\n", "2025-05-26 20:22:13,597 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010206, 形状: (17, 21)\n", "2025-05-26 20:22:13,888 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010208, 形状: (13, 21)\n", "2025-05-26 20:22:14,022 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010209, 形状: (18, 21)\n", "2025-05-26 20:22:14,163 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010211, 形状: (12, 21)\n", "2025-05-26 20:22:14,260 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010210, 形状: (16, 21)\n", "2025-05-26 20:22:14,336 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010212, 形状: (11, 21)\n", "2025-05-26 20:22:14,476 - INFO - レース情報: 240件\n", "2025-05-26 20:22:14,477 - INFO - レース結果: 3620件\n", "2025-05-26 20:22:14,478 - INFO - 2. ベースデータを作成中...\n", "2025-05-26 20:22:14,482 - INFO - 3. レース情報を統合中...\n", "2025-05-26 20:22:14,487 - INFO - レース情報統合後: 3620件, 33カラム\n", "2025-05-26 20:22:14,488 - INFO - 4. 馬基本情報を統合中...\n", "2025-05-26 20:22:14,489 - INFO - レースから抽出した馬ID数: 1頭\n", "2025-05-26 20:22:14,490 - WARNING - 馬基本情報データが空のため、統合をスキップします\n", "2025-05-26 20:22:14,491 - INFO - 馬基本情報統合後: 3620件, 33カラム\n", "2025-05-26 20:22:14,491 - INFO - 5. 馬過去成績統計を統合中...\n", "2025-05-26 20:22:14,493 - INFO - レースから抽出した馬ID数: 1頭\n", "2025-05-26 20:22:14,493 - INFO - 馬過去成績統計の統合を開始します。\n", "2025-05-26 20:22:14,494 - WARNING - 馬過去成績データが空のため、過去成績統計の統合をスキップします\n", "2025-05-26 20:22:14,494 - INFO - 過去成績統計統合後: 3620件, 33カラム\n", "2025-05-26 20:22:14,494 - INFO - 包括的データ生成完了: 3620件, 33カラム\n", "2025-05-26 20:22:14,497 - INFO - 包括的な表形式データの生成を開始します\n", "2025-05-26 20:22:14,498 - INFO - 1. レース情報とレース結果を取得中...\n", "2025-05-26 20:22:14,499 - INFO - 処理対象のファイル数: 240\n"]}, {"name": "stdout", "output_type": "stream", "text": ["   ✅ 2023年: 3,620件, 34カラム\n", "      レース数: 240, 馬数: 1\n", "      処理時間: 40.7秒\n", "\n", "📅 [2/2] 2024年のデータを処理中...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "62980c48ef1b40d9b825a6b0c23a4779", "version_major": 2, "version_minor": 0}, "text/plain": ["HTML解析 (並列):   0%|          | 0/240 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-26 20:22:14,787 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010101, 形状: (16, 21)\n", "2025-05-26 20:22:14,992 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010103, 形状: (16, 21)\n", "2025-05-26 20:22:15,136 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010102, 形状: (16, 21)\n", "2025-05-26 20:22:15,241 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010104, 形状: (16, 21)\n", "2025-05-26 20:22:15,612 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010105, 形状: (16, 21)\n", "2025-05-26 20:22:15,776 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010106, 形状: (18, 21)\n", "2025-05-26 20:22:15,893 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010108, 形状: (16, 21)\n", "2025-05-26 20:22:16,078 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010107, 形状: (16, 21)\n", "2025-05-26 20:22:16,342 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010109, 形状: (16, 21)\n", "2025-05-26 20:22:16,605 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010110, 形状: (15, 21)\n", "2025-05-26 20:22:16,746 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010111, 形状: (18, 21)\n", "2025-05-26 20:22:16,862 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010112, 形状: (16, 21)\n", "2025-05-26 20:22:17,056 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010201, 形状: (16, 21)\n", "2025-05-26 20:22:17,254 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010203, 形状: (16, 21)\n", "2025-05-26 20:22:17,456 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010202, 形状: (16, 21)\n", "2025-05-26 20:22:17,621 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010204, 形状: (16, 21)\n", "2025-05-26 20:22:17,787 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010205, 形状: (16, 21)\n", "2025-05-26 20:22:18,004 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010207, 形状: (12, 21)\n", "2025-05-26 20:22:18,164 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010206, 形状: (18, 21)\n", "2025-05-26 20:22:18,347 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010208, 形状: (14, 21)\n", "2025-05-26 20:22:18,668 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010209, 形状: (16, 21)\n", "2025-05-26 20:22:18,872 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010210, 形状: (15, 21)\n", "2025-05-26 20:22:19,010 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010211, 形状: (16, 21)\n", "2025-05-26 20:22:19,169 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010212, 形状: (16, 21)\n", "2025-05-26 20:22:19,339 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010301, 形状: (16, 21)\n", "2025-05-26 20:22:19,710 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010302, 形状: (16, 21)\n", "2025-05-26 20:22:19,877 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010303, 形状: (16, 21)\n", "2025-05-26 20:22:20,032 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010304, 形状: (16, 21)\n", "2025-05-26 20:22:20,163 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010305, 形状: (18, 21)\n", "2025-05-26 20:22:20,363 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010306, 形状: (13, 21)\n", "2025-05-26 20:22:20,485 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010309, 形状: (14, 21)\n", "2025-05-26 20:22:20,738 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010307, 形状: (16, 21)\n", "2025-05-26 20:22:20,873 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010308, 形状: (16, 21)\n", "2025-05-26 20:22:21,092 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010311, 形状: (13, 21)\n", "2025-05-26 20:22:21,235 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010310, 形状: (16, 21)\n", "2025-05-26 20:22:21,344 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010312, 形状: (10, 21)\n", "2025-05-26 20:22:21,566 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010401, 形状: (16, 21)\n", "2025-05-26 20:22:21,871 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010402, 形状: (16, 21)\n", "2025-05-26 20:22:22,035 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010403, 形状: (16, 21)\n", "2025-05-26 20:22:22,138 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010404, 形状: (16, 21)\n", "2025-05-26 20:22:22,419 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010405, 形状: (18, 21)\n", "2025-05-26 20:22:22,564 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010406, 形状: (16, 21)\n", "2025-05-26 20:22:22,721 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010408, 形状: (16, 21)\n", "2025-05-26 20:22:22,945 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010407, 形状: (18, 21)\n", "2025-05-26 20:22:23,190 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010410, 形状: (16, 21)\n", "2025-05-26 20:22:23,283 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010409, 形状: (14, 21)\n", "2025-05-26 20:22:23,425 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010412, 形状: (16, 21)\n", "2025-05-26 20:22:23,552 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010411, 形状: (16, 21)\n", "2025-05-26 20:22:23,743 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010501, 形状: (16, 21)\n", "2025-05-26 20:22:24,047 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010502, 形状: (16, 21)\n", "2025-05-26 20:22:24,127 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010504, 形状: (9, 21)\n", "2025-05-26 20:22:24,254 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010503, 形状: (16, 21)\n", "2025-05-26 20:22:24,488 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010505, 形状: (16, 21)\n", "2025-05-26 20:22:24,626 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010507, 形状: (11, 21)\n", "2025-05-26 20:22:24,780 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010506, 形状: (17, 21)\n", "2025-05-26 20:22:24,977 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010508, 形状: (14, 21)\n", "2025-05-26 20:22:25,187 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010509, 形状: (16, 21)\n", "2025-05-26 20:22:25,429 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010510, 形状: (16, 21)\n", "2025-05-26 20:22:25,560 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010512, 形状: (16, 21)\n", "2025-05-26 20:22:25,671 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010511, 形状: (16, 21)\n", "2025-05-26 20:22:25,936 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010601, 形状: (16, 21)\n", "2025-05-26 20:22:26,154 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010602, 形状: (16, 21)\n", "2025-05-26 20:22:26,268 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010604, 形状: (11, 21)\n", "2025-05-26 20:22:26,376 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010603, 形状: (16, 21)\n", "2025-05-26 20:22:26,629 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010605, 形状: (18, 21)\n", "2025-05-26 20:22:26,809 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010606, 形状: (16, 21)\n", "2025-05-26 20:22:27,008 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010607, 形状: (13, 21)\n", "2025-05-26 20:22:27,111 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010608, 形状: (16, 21)\n", "2025-05-26 20:22:27,288 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010609, 形状: (16, 21)\n", "2025-05-26 20:22:27,513 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010610, 形状: (16, 21)\n", "2025-05-26 20:22:27,659 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010612, 形状: (16, 21)\n", "2025-05-26 20:22:27,784 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010611, 形状: (16, 21)\n", "2025-05-26 20:22:28,022 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010701, 形状: (16, 21)\n", "2025-05-26 20:22:28,265 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010704, 形状: (11, 21)\n", "2025-05-26 20:22:28,376 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010702, 形状: (16, 21)\n", "2025-05-26 20:22:28,489 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010703, 形状: (16, 21)\n", "2025-05-26 20:22:28,641 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010705, 形状: (16, 21)\n", "2025-05-26 20:22:28,976 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010709, 形状: (12, 21)\n", "2025-05-26 20:22:29,071 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010706, 形状: (16, 21)\n", "2025-05-26 20:22:29,158 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010707, 形状: (13, 21)\n", "2025-05-26 20:22:29,253 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010708, 形状: (14, 21)\n", "2025-05-26 20:22:29,500 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010710, 形状: (16, 21)\n", "2025-05-26 20:22:29,642 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010711, 形状: (14, 21)\n", "2025-05-26 20:22:29,847 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010712, 形状: (16, 21)\n", "2025-05-26 20:22:29,954 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010801, 形状: (16, 21)\n", "2025-05-26 20:22:30,161 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010802, 形状: (16, 21)\n", "2025-05-26 20:22:30,358 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010804, 形状: (16, 21)\n", "2025-05-26 20:22:30,456 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010803, 形状: (16, 21)\n", "2025-05-26 20:22:30,533 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010806, 形状: (6, 21)\n", "2025-05-26 20:22:30,649 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010805, 形状: (18, 21)\n", "2025-05-26 20:22:30,970 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010809, 形状: (10, 21)\n", "2025-05-26 20:22:31,084 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010807, 形状: (16, 21)\n", "2025-05-26 20:22:31,199 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010808, 形状: (16, 21)\n", "2025-05-26 20:22:31,298 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010810, 形状: (13, 21)\n", "2025-05-26 20:22:31,520 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010811, 形状: (17, 21)\n", "2025-05-26 20:22:31,760 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010812, 形状: (16, 21)\n", "2025-05-26 20:22:31,874 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010901, 形状: (16, 21)\n", "2025-05-26 20:22:32,010 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010902, 形状: (16, 21)\n", "2025-05-26 20:22:32,203 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010903, 形状: (15, 21)\n", "2025-05-26 20:22:32,366 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010904, 形状: (16, 21)\n", "2025-05-26 20:22:32,629 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010905, 形状: (18, 21)\n", "2025-05-26 20:22:32,755 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010906, 形状: (16, 21)\n", "2025-05-26 20:22:32,852 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010907, 形状: (11, 21)\n", "2025-05-26 20:22:33,082 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010908, 形状: (14, 21)\n", "2025-05-26 20:22:33,201 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010909, 形状: (11, 21)\n", "2025-05-26 20:22:33,351 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010910, 形状: (15, 21)\n", "2025-05-26 20:22:33,511 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010911, 形状: (18, 21)\n", "2025-05-26 20:22:33,831 - INFO - pd.read_html でレース結果テーブルをパース成功: 202506010912, 形状: (14, 21)\n", "2025-05-26 20:22:33,959 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010101, 形状: (16, 21)\n", "2025-05-26 20:22:34,094 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010102, 形状: (16, 21)\n", "2025-05-26 20:22:34,218 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010103, 形状: (16, 21)\n", "2025-05-26 20:22:34,478 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010104, 形状: (16, 21)\n", "2025-05-26 20:22:34,656 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010107, 形状: (9, 21)\n", "2025-05-26 20:22:34,772 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010106, 形状: (16, 21)\n", "2025-05-26 20:22:34,887 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010105, 形状: (18, 21)\n", "2025-05-26 20:22:35,084 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010109, 形状: (11, 21)\n", "2025-05-26 20:22:35,205 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010108, 形状: (16, 21)\n", "2025-05-26 20:22:35,412 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010110, 形状: (16, 21)\n", "2025-05-26 20:22:35,653 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010111, 形状: (16, 21)\n", "2025-05-26 20:22:35,860 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010112, 形状: (16, 21)\n", "2025-05-26 20:22:36,009 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010201, 形状: (16, 21)\n", "2025-05-26 20:22:36,238 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010202, 形状: (16, 21)\n", "2025-05-26 20:22:36,416 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010203, 形状: (16, 21)\n", "2025-05-26 20:22:36,537 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010204, 形状: (12, 21)\n", "2025-05-26 20:22:36,875 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010205, 形状: (18, 21)\n", "2025-05-26 20:22:37,048 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010208, 形状: (16, 21)\n", "2025-05-26 20:22:37,165 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010206, 形状: (18, 21)\n", "2025-05-26 20:22:37,275 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010207, 形状: (16, 21)\n", "2025-05-26 20:22:37,474 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010209, 形状: (16, 21)\n", "2025-05-26 20:22:37,721 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010210, 形状: (18, 21)\n", "2025-05-26 20:22:37,882 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010211, 形状: (17, 21)\n", "2025-05-26 20:22:38,010 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010212, 形状: (16, 21)\n", "2025-05-26 20:22:38,197 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010301, 形状: (16, 21)\n", "2025-05-26 20:22:38,335 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010302, 形状: (16, 21)\n", "2025-05-26 20:22:38,587 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010303, 形状: (16, 21)\n", "2025-05-26 20:22:38,687 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010304, 形状: (16, 21)\n", "2025-05-26 20:22:38,801 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010306, 形状: (7, 21)\n", "2025-05-26 20:22:38,970 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010305, 形状: (18, 21)\n", "2025-05-26 20:22:39,112 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010308, 形状: (11, 21)\n", "2025-05-26 20:22:39,230 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010307, 形状: (16, 21)\n", "2025-05-26 20:22:39,327 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010309, 形状: (13, 21)\n", "2025-05-26 20:22:39,617 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010310, 形状: (16, 21)\n", "2025-05-26 20:22:39,787 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010401, 形状: (16, 21)\n", "2025-05-26 20:22:39,888 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010311, 形状: (16, 21)\n", "2025-05-26 20:22:40,011 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010312, 形状: (16, 21)\n", "2025-05-26 20:22:40,224 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010402, 形状: (16, 21)\n", "2025-05-26 20:22:40,445 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010403, 形状: (16, 21)\n", "2025-05-26 20:22:40,515 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010404, 形状: (12, 21)\n", "2025-05-26 20:22:40,667 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010405, 形状: (16, 21)\n", "2025-05-26 20:22:40,870 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010407, 形状: (16, 21)\n", "2025-05-26 20:22:40,995 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010406, 形状: (18, 21)\n", "2025-05-26 20:22:41,138 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010408, 形状: (16, 21)\n", "2025-05-26 20:22:41,393 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010409, 形状: (16, 21)\n", "2025-05-26 20:22:41,570 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010410, 形状: (16, 21)\n", "2025-05-26 20:22:41,713 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010411, 形状: (18, 21)\n", "2025-05-26 20:22:41,868 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010412, 形状: (18, 21)\n", "2025-05-26 20:22:42,041 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010501, 形状: (16, 21)\n", "2025-05-26 20:22:42,332 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010502, 形状: (16, 21)\n", "2025-05-26 20:22:42,496 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010503, 形状: (16, 21)\n", "2025-05-26 20:22:42,683 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010504, 形状: (15, 21)\n", "2025-05-26 20:22:42,859 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010506, 形状: (16, 21)\n", "2025-05-26 20:22:42,939 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010505, 形状: (16, 21)\n", "2025-05-26 20:22:43,269 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010507, 形状: (18, 21)\n", "2025-05-26 20:22:43,444 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010508, 形状: (16, 21)\n", "2025-05-26 20:22:43,572 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010509, 形状: (16, 21)\n", "2025-05-26 20:22:43,707 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010510, 形状: (16, 21)\n", "2025-05-26 20:22:43,913 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010511, 形状: (15, 21)\n", "2025-05-26 20:22:44,026 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010512, 形状: (14, 21)\n", "2025-05-26 20:22:44,122 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010601, 形状: (16, 21)\n", "2025-05-26 20:22:44,323 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010602, 形状: (11, 21)\n", "2025-05-26 20:22:44,573 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010603, 形状: (16, 21)\n", "2025-05-26 20:22:44,697 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010605, 形状: (18, 21)\n", "2025-05-26 20:22:44,831 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010604, 形状: (16, 21)\n", "2025-05-26 20:22:44,951 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010606, 形状: (16, 21)\n", "2025-05-26 20:22:45,251 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010608, 形状: (16, 21)\n", "2025-05-26 20:22:45,337 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010607, 形状: (12, 21)\n", "2025-05-26 20:22:45,486 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010610, 形状: (12, 21)\n", "2025-05-26 20:22:45,606 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010609, 形状: (16, 21)\n", "2025-05-26 20:22:45,813 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010611, 形状: (16, 21)\n", "2025-05-26 20:22:45,916 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010612, 形状: (13, 21)\n", "2025-05-26 20:22:46,173 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010702, 形状: (13, 21)\n", "2025-05-26 20:22:46,311 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010701, 形状: (16, 21)\n", "2025-05-26 20:22:46,513 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010703, 形状: (16, 21)\n", "2025-05-26 20:22:46,607 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010704, 形状: (10, 21)\n", "2025-05-26 20:22:46,796 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010706, 形状: (10, 21)\n", "2025-05-26 20:22:47,077 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010709, 形状: (11, 21)\n", "2025-05-26 20:22:47,300 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010707, 形状: (10, 21)\n", "2025-05-26 20:22:47,456 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010705, 形状: (18, 21)\n", "2025-05-26 20:22:47,604 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010708, 形状: (16, 21)\n", "2025-05-26 20:22:47,943 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010710, 形状: (16, 21)\n", "2025-05-26 20:22:48,155 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010711, 形状: (16, 21)\n", "2025-05-26 20:22:48,281 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010712, 形状: (16, 21)\n", "2025-05-26 20:22:48,421 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010801, 形状: (16, 21)\n", "2025-05-26 20:22:48,805 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010802, 形状: (16, 21)\n", "2025-05-26 20:22:48,977 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010803, 形状: (16, 21)\n", "2025-05-26 20:22:49,106 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010804, 形状: (16, 21)\n", "2025-05-26 20:22:49,282 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010805, 形状: (18, 21)\n", "2025-05-26 20:22:49,546 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010806, 形状: (16, 21)\n", "2025-05-26 20:22:49,749 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010807, 形状: (15, 21)\n", "2025-05-26 20:22:49,873 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010809, 形状: (10, 21)\n", "2025-05-26 20:22:50,016 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010808, 形状: (16, 21)\n", "2025-05-26 20:22:50,248 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010811, 形状: (14, 21)\n", "2025-05-26 20:22:50,358 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010810, 形状: (13, 21)\n", "2025-05-26 20:22:50,518 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010812, 形状: (12, 21)\n", "2025-05-26 20:22:50,607 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010901, 形状: (11, 21)\n", "2025-05-26 20:22:51,022 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010903, 形状: (11, 21)\n", "2025-05-26 20:22:51,049 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010902, 形状: (16, 21)\n", "2025-05-26 20:22:51,148 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010904, 形状: (8, 21)\n", "2025-05-26 20:22:51,318 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010905, 形状: (16, 21)\n", "2025-05-26 20:22:51,565 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010907, 形状: (16, 21)\n", "2025-05-26 20:22:51,703 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010906, 形状: (16, 21)\n", "2025-05-26 20:22:51,926 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010908, 形状: (13, 21)\n", "2025-05-26 20:22:52,027 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010909, 形状: (9, 21)\n", "2025-05-26 20:22:52,225 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010910, 形状: (14, 21)\n", "2025-05-26 20:22:52,364 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010911, 形状: (16, 21)\n", "2025-05-26 20:22:52,454 - INFO - pd.read_html でレース結果テーブルをパース成功: 202507010912, 形状: (13, 21)\n", "2025-05-26 20:22:52,575 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010101, 形状: (12, 21)\n", "2025-05-26 20:22:52,916 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010102, 形状: (17, 21)\n", "2025-05-26 20:22:52,985 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010104, 形状: (10, 21)\n", "2025-05-26 20:22:53,100 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010103, 形状: (13, 21)\n", "2025-05-26 20:22:53,160 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010105, 形状: (7, 21)\n", "2025-05-26 20:22:53,540 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010106, 形状: (18, 21)\n", "2025-05-26 20:22:53,643 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010107, 形状: (16, 21)\n", "2025-05-26 20:22:53,862 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010109, 形状: (12, 21)\n", "2025-05-26 20:22:53,998 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010108, 形状: (16, 21)\n", "2025-05-26 20:22:54,294 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010110, 形状: (16, 21)\n", "2025-05-26 20:22:54,414 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010201, 形状: (13, 21)\n", "2025-05-26 20:22:54,536 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010112, 形状: (16, 21)\n", "2025-05-26 20:22:54,680 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010111, 形状: (18, 21)\n", "2025-05-26 20:22:55,051 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010202, 形状: (17, 21)\n", "2025-05-26 20:22:55,240 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010203, 形状: (14, 21)\n", "2025-05-26 20:22:55,350 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010204, 形状: (12, 21)\n", "2025-05-26 20:22:55,501 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010205, 形状: (18, 21)\n", "2025-05-26 20:22:55,748 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010206, 形状: (17, 21)\n", "2025-05-26 20:22:55,999 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010207, 形状: (14, 21)\n", "2025-05-26 20:22:56,122 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010208, 形状: (13, 21)\n", "2025-05-26 20:22:56,317 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010209, 形状: (18, 21)\n", "2025-05-26 20:22:56,498 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010210, 形状: (16, 21)\n", "2025-05-26 20:22:56,613 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010211, 形状: (12, 21)\n", "2025-05-26 20:22:56,694 - INFO - pd.read_html でレース結果テーブルをパース成功: 202510010212, 形状: (11, 21)\n", "2025-05-26 20:22:56,899 - INFO - レース情報: 240件\n", "2025-05-26 20:22:56,900 - INFO - レース結果: 3620件\n", "2025-05-26 20:22:56,901 - INFO - 2. ベースデータを作成中...\n", "2025-05-26 20:22:56,905 - INFO - 3. レース情報を統合中...\n", "2025-05-26 20:22:56,911 - INFO - レース情報統合後: 3620件, 33カラム\n", "2025-05-26 20:22:56,911 - INFO - 4. 馬基本情報を統合中...\n", "2025-05-26 20:22:56,912 - INFO - レースから抽出した馬ID数: 1頭\n", "2025-05-26 20:22:56,914 - WARNING - 馬基本情報データが空のため、統合をスキップします\n", "2025-05-26 20:22:56,914 - INFO - 馬基本情報統合後: 3620件, 33カラム\n", "2025-05-26 20:22:56,915 - INFO - 5. 馬過去成績統計を統合中...\n", "2025-05-26 20:22:56,916 - INFO - レースから抽出した馬ID数: 1頭\n", "2025-05-26 20:22:56,916 - INFO - 馬過去成績統計の統合を開始します。\n", "2025-05-26 20:22:56,918 - WARNING - 馬過去成績データが空のため、過去成績統計の統合をスキップします\n", "2025-05-26 20:22:56,918 - INFO - 過去成績統計統合後: 3620件, 33カラム\n", "2025-05-26 20:22:56,919 - INFO - 包括的データ生成完了: 3620件, 33カラム\n"]}, {"name": "stdout", "output_type": "stream", "text": ["   ✅ 2024年: 3,620件, 34カラム\n", "      レース数: 240, 馬数: 1\n", "      処理時間: 42.4秒\n", "\n", "🎉 全年度統合完了!\n", "   📊 総データ件数: 7,240件\n", "   📋 カラム数: 34個\n", "   📅 対象年度: 2023, 2024\n", "   🏁 総ユニークレース数: 240\n", "   🐎 総ユニーク馬数: 1\n", "   ⏱️ 総処理時間: 83.1秒 (1.4分)\n", "\n", "📊 年度別サマリー:\n", "   2023年: 3,620件, レース240, 馬1, 母父0.0%, 40.7秒\n", "   2024年: 3,620件, レース240, 馬1, 母父0.0%, 42.4秒\n"]}], "source": ["# 包括的データの生成（複数年度対応）\n", "print(\"\\n📊 包括的データ統合を実行中...\")\n", "print(\"含める情報:\")\n", "print(\"  ✅ レース情報（天気、馬場状態、距離等）\")\n", "print(\"  ✅ 馬基本情報（血統、調教師、馬主等）\")\n", "print(\"  ✅ 馬過去成績統計（直近N戦の統計）\")\n", "print(\"  🎯 母父情報も含む！\")\n", "\n", "# 複数年度のデータを統合\n", "all_comprehensive_dfs = []\n", "year_summaries = {}\n", "processing_start_time = datetime.now()\n", "\n", "for i, year in enumerate(TARGET_YEARS, 1):\n", "    print(f\"\\n📅 [{i}/{len(TARGET_YEARS)}] {year}年のデータを処理中...\")\n", "    year_start_time = datetime.now()\n", "    \n", "    year_df = integrator.generate_comprehensive_table(\n", "        year=year,\n", "        include_race_info=True,      # レース情報を含める\n", "        include_horse_info=True,     # 馬基本情報（血統含む）を含める\n", "        include_past_performance=True,  # 馬過去成績統計を含める\n", "        performance_window_races=PERFORMANCE_WINDOWS,\n", "        parallel=True,\n", "        max_workers=MAX_WORKERS\n", "    )\n", "    \n", "    year_end_time = datetime.now()\n", "    year_duration = (year_end_time - year_start_time).total_seconds()\n", "    \n", "    if not year_df.empty:\n", "        # 年度情報を追加\n", "        year_df['year'] = year\n", "        all_comprehensive_dfs.append(year_df)\n", "        \n", "        # 年度別サマリーを記録\n", "        year_summaries[year] = {\n", "            'records': len(year_df),\n", "            'columns': len(year_df.columns),\n", "            'unique_races': year_df['race_id'].nunique() if 'race_id' in year_df.columns else 0,\n", "            'unique_horses': year_df['horse_id'].nunique() if 'horse_id' in year_df.columns else 0,\n", "            'mother_father_coverage': (year_df['mother_father_name'].notna().sum() / len(year_df)) * 100 if 'mother_father_name' in year_df.columns else 0,\n", "            'processing_time': year_duration\n", "        }\n", "        \n", "        print(f\"   ✅ {year}年: {len(year_df):,}件, {len(year_df.columns)}カラム\")\n", "        print(f\"      レース数: {year_df['race_id'].nunique() if 'race_id' in year_df.columns else 0:,}, 馬数: {year_df['horse_id'].nunique() if 'horse_id' in year_df.columns else 0:,}\")\n", "        if 'mother_father_name' in year_df.columns:\n", "            mother_father_rate = (year_df['mother_father_name'].notna().sum() / len(year_df)) * 100\n", "            print(f\"      母父情報カバー率: {mother_father_rate:.1f}%\")\n", "        print(f\"      処理時間: {year_duration:.1f}秒\")\n", "    else:\n", "        print(f\"   ❌ {year}年のデータ生成に失敗しました\")\n", "        year_summaries[year] = {\n", "            'records': 0,\n", "            'columns': 0,\n", "            'unique_races': 0,\n", "            'unique_horses': 0,\n", "            'mother_father_coverage': 0,\n", "            'processing_time': year_duration\n", "        }\n", "\n", "processing_end_time = datetime.now()\n", "total_duration = (processing_end_time - processing_start_time).total_seconds()\n", "\n", "# 全年度のデータを結合\n", "if all_comprehensive_dfs:\n", "    comprehensive_df = pd.concat(all_comprehensive_dfs, ignore_index=True)\n", "    print(f\"\\n🎉 全年度統合完了!\")\n", "    print(f\"   📊 総データ件数: {len(comprehensive_df):,}件\")\n", "    print(f\"   📋 カラム数: {len(comprehensive_df.columns)}個\")\n", "    print(f\"   📅 対象年度: {', '.join(TARGET_YEARS)}\")\n", "    print(f\"   🏁 総ユニークレース数: {comprehensive_df['race_id'].nunique() if 'race_id' in comprehensive_df.columns else 0:,}\")\n", "    print(f\"   🐎 総ユニーク馬数: {comprehensive_df['horse_id'].nunique() if 'horse_id' in comprehensive_df.columns else 0:,}\")\n", "    print(f\"   ⏱️ 総処理時間: {total_duration:.1f}秒 ({total_duration/60:.1f}分)\")\n", "    \n", "    # 年度別サマリーの表示\n", "    print(f\"\\n📊 年度別サマリー:\")\n", "    for year, summary in year_summaries.items():\n", "        print(f\"   {year}年: {summary['records']:,}件, レース{summary['unique_races']:,}, 馬{summary['unique_horses']:,}, 母父{summary['mother_father_coverage']:.1f}%, {summary['processing_time']:.1f}秒\")\n", "else:\n", "    comprehensive_df = pd.DataFrame()\n", "    print(\"   ❌ 全年度のデータ生成に失敗しました\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "着順", "rawType": "object", "type": "unknown"}, {"name": "枠番", "rawType": "object", "type": "unknown"}, {"name": "馬番", "rawType": "object", "type": "unknown"}, {"name": "馬名", "rawType": "object", "type": "string"}, {"name": "性齢", "rawType": "object", "type": "string"}, {"name": "斤量", "rawType": "object", "type": "unknown"}, {"name": "騎手", "rawType": "object", "type": "string"}, {"name": "タイム", "rawType": "object", "type": "unknown"}, {"name": "着差", "rawType": "object", "type": "unknown"}, {"name": "単勝", "rawType": "object", "type": "unknown"}, {"name": "人気", "rawType": "object", "type": "unknown"}, {"name": "馬体重", "rawType": "object", "type": "string"}, {"name": "調教師", "rawType": "object", "type": "string"}, {"name": "race_id", "rawType": "object", "type": "string"}, {"name": "horse_id", "rawType": "object", "type": "string"}, {"name": "jockey_id", "rawType": "object", "type": "string"}, {"name": "trainer_id", "rawType": "object", "type": "string"}, {"name": "開催", "rawType": "object", "type": "string"}, {"name": "date", "rawType": "object", "type": "string"}, {"name": "レース名", "rawType": "object", "type": "string"}, {"name": "course_len", "rawType": "int64", "type": "integer"}, {"name": "weather", "rawType": "object", "type": "string"}, {"name": "race_type", "rawType": "object", "type": "string"}, {"name": "ground_state", "rawType": "object", "type": "string"}, {"name": "around", "rawType": "object", "type": "string"}, {"name": "着順_mean_last_5", "rawType": "float64", "type": "float"}, {"name": "着順_std_last_5", "rawType": "float64", "type": "float"}, {"name": "着順_min_last_5", "rawType": "float64", "type": "float"}, {"name": "着順_max_last_5", "rawType": "float64", "type": "float"}, {"name": "人気_mean_last_5", "rawType": "float64", "type": "float"}, {"name": "人気_std_last_5", "rawType": "float64", "type": "float"}, {"name": "人気_min_last_5", "rawType": "float64", "type": "float"}, {"name": "人気_max_last_5", "rawType": "float64", "type": "float"}, {"name": "オッズ_mean_last_5", "rawType": "float64", "type": "float"}, {"name": "オッズ_std_last_5", "rawType": "float64", "type": "float"}, {"name": "オッズ_min_last_5", "rawType": "float64", "type": "float"}, {"name": "オッズ_max_last_5", "rawType": "float64", "type": "float"}, {"name": "着順_mean_last_10", "rawType": "float64", "type": "float"}, {"name": "着順_std_last_10", "rawType": "float64", "type": "float"}, {"name": "着順_min_last_10", "rawType": "float64", "type": "float"}, {"name": "着順_max_last_10", "rawType": "float64", "type": "float"}, {"name": "人気_mean_last_10", "rawType": "float64", "type": "float"}, {"name": "人気_std_last_10", "rawType": "float64", "type": "float"}, {"name": "人気_min_last_10", "rawType": "float64", "type": "float"}, {"name": "人気_max_last_10", "rawType": "float64", "type": "float"}, {"name": "オッズ_mean_last_10", "rawType": "float64", "type": "float"}, {"name": "オッズ_std_last_10", "rawType": "float64", "type": "float"}, {"name": "オッズ_min_last_10", "rawType": "float64", "type": "float"}, {"name": "オッズ_max_last_10", "rawType": "float64", "type": "float"}, {"name": "year", "rawType": "object", "type": "string"}], "ref": "b176c819-9b0c-40da-b0b6-c61a84268d46", "rows": [["0", "1", "5", "5", "サトミノキラリ", "牡2", "55.0", "横山武史", "1:09.5", null, "1.2", "1.0", "452(-4)", "[東] 鈴木伸尋", "202301010101", "2021101429", "01170", "01026", "01", "2023年01月01日", "2歳未勝利", "12", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["1", "2", "8", "8", "ベアゴーゴー", "牝2", "55.0", "浜中俊", "1:09.5", "クビ", "4.1", "2.0", "454(+2)", "[東] 杉浦宏昭", "202301010101", "2021105872", "01115", "01008", "01", "2023年01月01日", "2歳未勝利", "12", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["2", "3", "6", "6", "ハピアーザンエバー", "牡2", "55.0", "藤岡佑介", "1:10.0", "2.1/2", "59.9", "6.0", "438(-6)", "[西] 羽月友彦", "202301010101", "2021106854", "01093", "01091", "01", "2023年01月01日", "2歳未勝利", "12", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["3", "4", "4", "4", "デビルシズカチャン", "牝2", "55.0", "ルメール", "1:10.2", "1.1/2", "16.6", "3.0", "450(+2)", "[西] 武幸四郎", "202301010101", "2021105553", "05339", "01160", "01", "2023年01月01日", "2歳未勝利", "12", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["4", "5", "1", "1", "ウィスピースノー", "牝2", "55.0", "吉田隼人", "1:10.3", "1/2", "23.9", "5.0", "434(-10)", "[西] 今野貞一", "202301010101", "2021100648", "01095", "01128", "01", "2023年01月01日", "2歳未勝利", "12", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["5", "6", "2", "2", "ロードスタウト", "牡2", "55.0", "鮫島克駿", "1:10.7", "2.1/2", "61.8", "7.0", "454(-6)", "[西] 中村直也", "202301010101", "2021100159", "01157", "01186", "01", "2023年01月01日", "2歳未勝利", "12", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["6", "7", "3", "3", "コミックガール", "牝2", "53.0", "佐々木大", "1:10.9", "1.1/4", "18.8", "4.0", "404(-2)", "[東] 上原佑紀", "202301010101", "2021100265", "01197", "01192", "01", "2023年01月01日", "2歳未勝利", "12", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["7", "8", "7", "7", "ヒポカンポ", "牝2", "52.0", "小林勝太", "1:11.9", "6", "251.3", "8.0", "394(-2)", "[東] 伊藤圭三", "202301010101", "2021103762", "01205", "01023", "01", "2023年01月01日", "2歳未勝利", "12", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["8", "1", "7", "9", "クリーデンス", "牝3", "54.0", "ルメール", "0:58.7", null, "2.4", "1.0", "496(+12)", "[西] 吉村圭司", "202301010102", "2020106547", "05339", "01130", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["9", "2", "6", "8", "ビップナージャ", "牝3", "54.0", "横山武史", "0:59.6", "5", "15.1", "5.0", "420(-6)", "[東] 高柳瑞樹", "202301010102", "2020100879", "01170", "01118", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["10", "3", "3", "3", "リキサンクー", "牡3", "53.0", "小林勝太", "0:59.6", "ハナ", "99.0", "11.0", "446(-4)", "[東] 奥平雅士", "202301010102", "2020100391", "01205", "01074", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["11", "4", "4", "4", "イミュータブル", "牡3", "56.0", "鮫島克駿", "0:59.8", "1.1/4", "8.6", "4.0", "474(+10)", "[西] 浜田多実", "202301010102", "2020104346", "01157", "01138", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["12", "5", "6", "7", "ステラヴェスパー", "牝3", "54.0", "藤岡佑介", "1:00.0", "1.1/4", "31.8", "9.0", "430(-4)", "[西] 茶木太樹", "202301010102", "2020102908", "01093", "01181", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["13", "6", "5", "5", "ディーン", "牝3", "54.0", "横山和生", "1:00.0", "ハナ", "5.2", "3.0", "460(+2)", "[東] 石栗龍彦", "202301010102", "2020100551", "01140", "01043", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["14", "7", "1", "1", "トウケイチョウテン", "牡3", "56.0", "斎藤新", "1:00.3", "1.3/4", "243.5", "12.0", "454(-2)", "[東] 辻哲英", "202301010102", "2020105380", "01178", "01182", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["15", "8", "5", "6", "マジカルガール", "牝3", "54.0", "黛弘人", "1:00.3", "クビ", "17.0", "6.0", "412(+2)", "[東] 嘉藤貴行", "202301010102", "2020104250", "01109", "01195", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["16", "9", "7", "10", "グランツベリー", "牝3", "51.0", "今村聖奈", "1:00.6", "1.3/4", "3.5", "2.0", "450(+4)", "[西] 田中克典", "202301010102", "2020104087", "01193", "01180", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["17", "10", "8", "11", "エイユーマックス", "牡3", "55.0", "小沢大仁", "1:00.8", "3/4", "22.0", "7.0", "476(-2)", "[西] 岡田稲男", "202301010102", "2020106395", "01185", "01066", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["18", "11", "2", "2", "ジュレーヴ", "牡3", "54.0", "佐々木大", "1:00.9", "1", "59.5", "10.0", "442(+8)", "[東] 堀内岳志", "202301010102", "2020107102", "01197", "01189", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["19", "12", "8", "12", "メジャーエール", "牝3", "53.0", "松本大輝", "1:01.1", "1", "31.7", "8.0", "450(+4)", "[東] 深山雅史", "202301010102", "2020101946", "01191", "01174", "01", "2023年01月01日", "3歳未勝利", "10", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["20", "1", "7", "11", "ケイアイメキラ", "セ3", "56.0", "亀田温心", "1:47.6", null, "8.4", "6.0", "462(+4)", "[西] 村山明", "202301010103", "2020104215", "01176", "01107", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["21", "2", "3", "4", "ホウオウプレシャス", "牡3", "56.0", "丹内祐次", "1:47.7", "1/2", "3.4", "1.0", "496(+2)", "[東] 栗田徹", "202301010103", "2020103939", "01091", "01127", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["22", "3", "6", "9", "ナイトスピアー", "牡3", "56.0", "横山和生", "1:48.2", "3", "4.8", "2.0", "472(0)", "[東] 小野次郎", "202301010103", "2020102347", "01140", "01125", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["23", "4", "3", "3", "ジラルデ", "牡3", "56.0", "池添謙一", "1:48.3", "クビ", "6.2", "4.0", "468(+12)", "[西] 飯田祐史", "202301010103", "2020105632", "01032", "01139", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["24", "5", "7", "12", "レイジングスラスト", "牡3", "56.0", "北村友一", "1:48.5", "1.1/2", "5.7", "3.0", "536(+14)", "[西] 矢作芳人", "202301010103", "2020103126", "01102", "01075", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["25", "6", "6", "10", "アルセリア", "牝3", "52.0", "佐々木大", "1:48.7", "1.1/2", "7.4", "5.0", "430(-8)", "[東] 和田雄二", "202301010103", "2020100863", "01197", "01143", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["26", "7", "5", "7", "エイチエヌバンピー", "牡3", "56.0", "山田敬士", "1:48.8", "クビ", "214.9", "14.0", "430(-17)", "[東] 小桧山悟", "202301010103", "2020105061", "01173", "01005", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["27", "8", "8", "13", "グレートキングベア", "牡3", "56.0", "横山武史", "1:48.9", "1", "20.1", "8.0", "452(-2)", "[東] 鈴木伸尋", "202301010103", "2020101252", "01170", "01026", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["28", "9", "4", "5", "ロードイモータル", "牡3", "56.0", "鮫島克駿", "1:49.0", "クビ", "15.1", "7.0", "502(+8)", "[東] 和田勇介", "202301010103", "2020101681", "01157", "01165", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["29", "10", "5", "8", "シャンデル", "牝3", "54.0", "斎藤新", "1:49.5", "3", "67.5", "9.0", "474(+12)", "[東] 青木孝文", "202301010103", "2020102875", "01178", "01156", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["30", "11", "8", "14", "オレノアイバ", "牡3", "52.0", "古川奈穂", "1:50.2", "4", "175.3", "12.0", "446(-8)", "[西] 高橋康之", "202301010103", "2020105472", "01190", "01135", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["31", "12", "1", "1", "スングリダンダン", "牝3", "54.0", "黛弘人", "1:50.6", "2", "179.8", "13.0", "446(+6)", "[東] 南田美知", "202301010103", "2020106970", "01109", "00437", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["32", "13", "4", "6", "イズジョーノバトン", "牝3", "51.0", "今村聖奈", "1:51.0", "2.1/2", "92.5", "11.0", "468(-6)", "[東] 本間忍", "202301010103", "2020100036", "01193", "01056", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["33", "14", "2", "2", "カンパナーダス", "牡3", "55.0", "横山琉人", "1:52.2", "7", "81.9", "10.0", "490(-14)", "[東] 久保田貴", "202301010103", "2020101849", "01192", "01067", "01", "2023年01月01日", "3歳未勝利", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["34", "1", "6", "9", "サクセスアイ", "牝3", "54.0", "亀田温心", "1:27.7", null, "19.7", "8.0", "426(+8)", "[西] 北出成人", "202301010104", "2020105354", "01176", "01078", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["35", "2", "1", "1", "ヒラリ", "牝3", "54.0", "ルメール", "1:27.7", "アタマ", "4.1", "2.0", "456(-4)", "[西] 武幸四郎", "202301010104", "2020103018", "05339", "01160", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["36", "3", "8", "13", "マルメラーダ", "牝3", "54.0", "横山武史", "1:28.1", "2", "3.8", "1.0", "444(+4)", "[東] 高橋裕", "202301010104", "2020105782", "01170", "00412", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["37", "4", "5", "6", "ルカン", "牝3", "54.0", "横山典弘", "1:28.1", "クビ", "8.3", "5.0", "418(0)", "[西] 安田翔伍", "202301010104", "2020102563", "00660", "01164", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["38", "5", "4", "4", "アラメダ", "牝3", "54.0", "浜中俊", "1:28.2", "3/4", "9.9", "6.0", "442(+6)", "[西] 池江泰寿", "202301010104", "2020103346", "01115", "01071", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["39", "6", "7", "10", "クライノート", "牝3", "54.0", "丹内祐次", "1:28.4", "3/4", "6.2", "4.0", "448(+14)", "[東] 畠山吉宏", "202301010104", "2020105792", "01091", "01048", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["40", "7", "2", "2", "エルフサイエン", "牝3", "53.0", "横山琉人", "1:28.5", "1/2", "112.5", "11.0", "434(-6)", "[東] 小島茂之", "202301010104", "2020105059", "01192", "01068", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["41", "8", "3", "3", "ロゼアクアリオ", "牝3", "53.0", "小沢大仁", "1:28.6", "1/2", "197.1", "12.0", "404(+20)", "[西] 高柳大輔", "202301010104", "2020104015", "01185", "01159", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["42", "9", "5", "7", "マノレア", "牝3", "52.0", "小林凌大", "1:28.8", "1.1/2", "326.0", "13.0", "454(+4)", "[西] 西園正都", "202301010104", "2020105587", "01177", "01028", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["43", "10", "7", "11", "リスノワール", "牝3", "54.0", "勝浦正樹", "1:28.9", "クビ", "20.2", "9.0", "462(0)", "[東] 村田一誠", "202301010104", "2020103104", "01025", "01190", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["44", "11", "8", "12", "レーヴスレアリーズ", "牝3", "51.0", "今村聖奈", "1:29.0", "3/4", "19.0", "7.0", "456(-4)", "[西] 寺島良", "202301010104", "2020106011", "01193", "01158", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["45", "12", "6", "8", "スマートヴィーヴル", "牝3", "50.0", "古川奈穂", "1:29.5", "3", "5.2", "3.0", "462(+4)", "[西] 矢作芳人", "202301010104", "2020104556", "01190", "01075", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["46", "13", "4", "5", "フューチャーアゲン", "牝3", "52.0", "佐々木大", "1:30.1", "3.1/2", "82.8", "10.0", "442(-12)", "[東] 加藤和宏", "202301010104", "2020106487", "01197", "01077", "01", "2023年01月01日", "3歳未勝利", "15", "晴", "芝", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["47", "1", "4", "4", "アセレラシオン", "牡2", "55.0", "鮫島克駿", "1:48.6", null, "4.6", "3.0", "524(0)", "[西] 浜田多実", "202301010105", "2021106689", "01157", "01138", "01", "2023年01月01日", "2歳新馬", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["48", "2", "8", "10", "カリフォルニア", "牡2", "55.0", "ルメール", "1:49.1", "3", "3.1", "1.0", "482(0)", "[東] 伊藤圭三", "202301010105", "2021103728", "05339", "01023", "01", "2023年01月01日", "2歳新馬", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"], ["49", "3", "7", "7", "ラファールドール", "牡2", "55.0", "横山武史", "1:49.6", "3", "6.2", "4.0", "504(0)", "[東] 田島俊明", "202301010105", "2021104889", "01170", "01112", "01", "2023年01月01日", "2歳新馬", "17", "晴", "ダート", "良", "", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2023"]], "shape": {"columns": 50, "rows": 94853}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>着順</th>\n", "      <th>枠番</th>\n", "      <th>馬番</th>\n", "      <th>馬名</th>\n", "      <th>性齢</th>\n", "      <th>斤量</th>\n", "      <th>騎手</th>\n", "      <th>タイム</th>\n", "      <th>着差</th>\n", "      <th>単勝</th>\n", "      <th>...</th>\n", "      <th>着順_max_last_10</th>\n", "      <th>人気_mean_last_10</th>\n", "      <th>人気_std_last_10</th>\n", "      <th>人気_min_last_10</th>\n", "      <th>人気_max_last_10</th>\n", "      <th>オッズ_mean_last_10</th>\n", "      <th>オッズ_std_last_10</th>\n", "      <th>オッズ_min_last_10</th>\n", "      <th>オッズ_max_last_10</th>\n", "      <th>year</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>サトミノキラリ</td>\n", "      <td>牡2</td>\n", "      <td>55.0</td>\n", "      <td>横山武史</td>\n", "      <td>1:09.5</td>\n", "      <td>NaN</td>\n", "      <td>1.2</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>8</td>\n", "      <td>ベアゴーゴー</td>\n", "      <td>牝2</td>\n", "      <td>55.0</td>\n", "      <td>浜中俊</td>\n", "      <td>1:09.5</td>\n", "      <td>クビ</td>\n", "      <td>4.1</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>ハピアーザンエバー</td>\n", "      <td>牡2</td>\n", "      <td>55.0</td>\n", "      <td>藤岡佑介</td>\n", "      <td>1:10.0</td>\n", "      <td>2.1/2</td>\n", "      <td>59.9</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>デビルシズカチャン</td>\n", "      <td>牝2</td>\n", "      <td>55.0</td>\n", "      <td>ルメール</td>\n", "      <td>1:10.2</td>\n", "      <td>1.1/2</td>\n", "      <td>16.6</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>ウィスピースノー</td>\n", "      <td>牝2</td>\n", "      <td>55.0</td>\n", "      <td>吉田隼人</td>\n", "      <td>1:10.3</td>\n", "      <td>1/2</td>\n", "      <td>23.9</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94848</th>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>7</td>\n", "      <td>グランデスフィーダ</td>\n", "      <td>牡4</td>\n", "      <td>58</td>\n", "      <td>荻野極</td>\n", "      <td>1:46.6</td>\n", "      <td>2</td>\n", "      <td>32.3</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2024</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94849</th>\n", "      <td>12</td>\n", "      <td>6</td>\n", "      <td>10</td>\n", "      <td>アメリカンチーフ</td>\n", "      <td>牡3</td>\n", "      <td>54</td>\n", "      <td>西塚洸二</td>\n", "      <td>1:46.8</td>\n", "      <td>1.1/4</td>\n", "      <td>51.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2024</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94850</th>\n", "      <td>13</td>\n", "      <td>5</td>\n", "      <td>8</td>\n", "      <td>シブースト</td>\n", "      <td>牝4</td>\n", "      <td>53</td>\n", "      <td>吉村誠之</td>\n", "      <td>1:49.0</td>\n", "      <td>大</td>\n", "      <td>167.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2024</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94851</th>\n", "      <td>14</td>\n", "      <td>8</td>\n", "      <td>15</td>\n", "      <td>ペイシャコパ</td>\n", "      <td>牝3</td>\n", "      <td>50</td>\n", "      <td>河原田菜</td>\n", "      <td>1:49.1</td>\n", "      <td>3/4</td>\n", "      <td>85.7</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2024</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94852</th>\n", "      <td>15</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>オリーボーレン</td>\n", "      <td>牝3</td>\n", "      <td>53</td>\n", "      <td>坂井瑠星</td>\n", "      <td>1:54.7</td>\n", "      <td>大</td>\n", "      <td>10.6</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2024</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>94853 rows × 50 columns</p>\n", "</div>"], "text/plain": ["       着順 枠番  馬番         馬名  性齢    斤量    騎手     タイム     着差     単勝  ...  \\\n", "0       1  5   5    サトミノキラリ  牡2  55.0  横山武史  1:09.5    NaN    1.2  ...   \n", "1       2  8   8     ベアゴーゴー  牝2  55.0   浜中俊  1:09.5     クビ    4.1  ...   \n", "2       3  6   6  ハピアーザンエバー  牡2  55.0  藤岡佑介  1:10.0  2.1/2   59.9  ...   \n", "3       4  4   4  デビルシズカチャン  牝2  55.0  ルメール  1:10.2  1.1/2   16.6  ...   \n", "4       5  1   1   ウィスピースノー  牝2  55.0  吉田隼人  1:10.3    1/2   23.9  ...   \n", "...    .. ..  ..        ...  ..   ...   ...     ...    ...    ...  ...   \n", "94848  11  4   7  グランデスフィーダ  牡4    58   荻野極  1:46.6      2   32.3  ...   \n", "94849  12  6  10   アメリカンチーフ  牡3    54  西塚洸二  1:46.8  1.1/4   51.0  ...   \n", "94850  13  5   8      シブースト  牝4    53  吉村誠之  1:49.0      大  167.0  ...   \n", "94851  14  8  15     ペイシャコパ  牝3    50  河原田菜  1:49.1    3/4   85.7  ...   \n", "94852  15  1   1    オリーボーレン  牝3    53  坂井瑠星  1:54.7      大   10.6  ...   \n", "\n", "      着順_max_last_10 人気_mean_last_10 人気_std_last_10 人気_min_last_10  \\\n", "0                NaN             NaN            NaN            NaN   \n", "1                NaN             NaN            NaN            NaN   \n", "2                NaN             NaN            NaN            NaN   \n", "3                NaN             NaN            NaN            NaN   \n", "4                NaN             NaN            NaN            NaN   \n", "...              ...             ...            ...            ...   \n", "94848            NaN             NaN            NaN            NaN   \n", "94849            NaN             NaN            NaN            NaN   \n", "94850            NaN             NaN            NaN            NaN   \n", "94851            NaN             NaN            NaN            NaN   \n", "94852            NaN             NaN            NaN            NaN   \n", "\n", "      人気_max_last_10 オッズ_mean_last_10 オッズ_std_last_10 オッズ_min_last_10  \\\n", "0                NaN              NaN             NaN             NaN   \n", "1                NaN              NaN             NaN             NaN   \n", "2                NaN              NaN             NaN             NaN   \n", "3                NaN              NaN             NaN             NaN   \n", "4                NaN              NaN             NaN             NaN   \n", "...              ...              ...             ...             ...   \n", "94848            NaN              NaN             NaN             NaN   \n", "94849            NaN              NaN             NaN             NaN   \n", "94850            NaN              NaN             NaN             NaN   \n", "94851            NaN              NaN             NaN             NaN   \n", "94852            NaN              NaN             NaN             NaN   \n", "\n", "      オッズ_max_last_10  year  \n", "0                 NaN  2023  \n", "1                 NaN  2023  \n", "2                 NaN  2023  \n", "3                 NaN  2023  \n", "4                 NaN  2023  \n", "...               ...   ...  \n", "94848             NaN  2024  \n", "94849             NaN  2024  \n", "94850             NaN  2024  \n", "94851             NaN  2024  \n", "94852             NaN  2024  \n", "\n", "[94853 rows x 50 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["comprehensive_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 複数年度データの構造確認"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 複数年度統合データの詳細分析\n", "==================================================\n", "\n", "📈 基本統計:\n", "   総レコード数: 47,181\n", "   総カラム数: 50\n", "   ユニークレース数: 3,454\n", "   ユニーク馬数: 11,786\n", "\n", "📅 年度別データ分布:\n", "   2023年: 47,672件 (50.3%)\n", "   2024年: 47,181件 (49.7%)\n", "\n", "📋 カラム分類:\n", "   race_info_columns: 1個\n", "     - レース名\n", "   horse_info_columns: 1個\n", "     - 調教師\n", "   performance_columns: 24個\n", "     - 着順_mean_last_5\n", "     - 着順_std_last_5\n", "     - 着順_min_last_5\n", "     - 着順_max_last_5\n", "     - 人気_mean_last_5\n", "     ... 他19個\n", "   basic_columns: 7個\n", "     - 着順\n", "     - 馬名\n", "     - 斤量\n", "     - 騎手\n", "     - 人気\n", "     ... 他2個\n"]}], "source": ["# 複数年度データ構造の詳細確認\n", "if not comprehensive_df.empty:\n", "    print(\"📊 複数年度統合データの詳細分析\")\n", "    print(\"=\" * 50)\n", "    \n", "    # データ概要の取得\n", "    summary = integrator.get_data_summary()\n", "    \n", "    print(f\"\\n📈 基本統計:\")\n", "    print(f\"   総レコード数: {summary['total_records']:,}\")\n", "    print(f\"   総カラム数: {summary['total_columns']}\")\n", "    print(f\"   ユニークレース数: {summary['unique_races']:,}\")\n", "    print(f\"   ユニーク馬数: {summary['unique_horses']:,}\")\n", "    \n", "    # 年度別分布\n", "    if 'year' in comprehensive_df.columns:\n", "        year_distribution = comprehensive_df['year'].value_counts().sort_index()\n", "        print(f\"\\n📅 年度別データ分布:\")\n", "        for year, count in year_distribution.items():\n", "            percentage = (count / len(comprehensive_df)) * 100\n", "            print(f\"   {year}年: {count:,}件 ({percentage:.1f}%)\")\n", "    \n", "    print(f\"\\n📋 カラム分類:\")\n", "    for category, columns in summary[\"data_columns\"].items():\n", "        if columns:\n", "            print(f\"   {category}: {len(columns)}個\")\n", "            # 最初の5個のカラム名を表示\n", "            for col in columns[:5]:\n", "                print(f\"     - {col}\")\n", "            if len(columns) > 5:\n", "                print(f\"     ... 他{len(columns) - 5}個\")\n", "else:\n", "    print(\"❌ データが生成されていません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4 データ結合品質チェック実行"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# データ結合品質チェックの実行\n", "if not comprehensive_df.empty:\n", "    print(\"🔍 データ結合品質チェックを実行中...\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 品質チェック実行\n", "    quality_report = check_data_merge_quality(comprehensive_df)\n", "    \n", "    print(\"\\n📋 品質チェック結果サマリー:\")\n", "    print(f\"   総レコード数: {quality_report['total_records']:,}\")\n", "    print(f\"   総カラム数: {quality_report['total_columns']}\")\n", "    \n", "    # 結合キーの品質評価\n", "    merge_keys = ['race_id', 'horse_id']\n", "    print(\"\\n🔑 結合キー品質評価:\")\n", "    for key in merge_keys:\n", "        if f'{key}_null_rate' in quality_report:\n", "            null_rate = quality_report[f'{key}_null_rate']\n", "            if null_rate < 1:\n", "                status = \"✅ 優秀\"\n", "            elif null_rate < 5:\n", "                status = \"⚠️ 良好\"\n", "            else:\n", "                status = \"❌ 要改善\"\n", "            print(f\"   {key}: {status} (欠損率: {null_rate:.2f}%)\")\n", "    \n", "    # データ型の一貫性評価\n", "    print(\"\\n🔧 データ型一貫性: ✅ 確認済み\")\n", "    \n", "    # 年度別データ分布の確認\n", "    if 'year' in comprehensive_df.columns:\n", "        year_distribution = comprehensive_df['year'].value_counts().sort_index()\n", "        print(\"\\n📅 年度別データ分布バランス:\")\n", "        total_records = len(comprehensive_df)\n", "        for year, count in year_distribution.items():\n", "            percentage = (count / total_records) * 100\n", "            balance_status = \"✅ バランス良好\" if 40 <= percentage <= 60 else \"⚠️ 偏りあり\"\n", "            print(f\"   {year}年: {count:,}件 ({percentage:.1f}%) {balance_status}\")\n", "    \n", "    print(\"\\n🎯 総合評価:\")\n", "    overall_score = 0\n", "    max_score = 0\n", "    \n", "    # 結合キー品質スコア\n", "    for key in merge_keys:\n", "        if f'{key}_null_rate' in quality_report:\n", "            null_rate = quality_report[f'{key}_null_rate']\n", "            if null_rate < 1:\n", "                overall_score += 10\n", "            elif null_rate < 5:\n", "                overall_score += 7\n", "            else:\n", "                overall_score += 3\n", "            max_score += 10\n", "    \n", "    # データ量スコア\n", "    if quality_report['total_records'] > 50000:\n", "        overall_score += 10\n", "    elif quality_report['total_records'] > 10000:\n", "        overall_score += 7\n", "    else:\n", "        overall_score += 5\n", "    max_score += 10\n", "    \n", "    final_score = (overall_score / max_score) * 100\n", "    \n", "    if final_score >= 90:\n", "        grade = \"🏆 優秀\"\n", "    elif final_score >= 80:\n", "        grade = \"✅ 良好\"\n", "    elif final_score >= 70:\n", "        grade = \"⚠️ 普通\"\n", "    else:\n", "        grade = \"❌ 要改善\"\n", "    \n", "    print(f\"   データ結合品質スコア: {final_score:.1f}/100 {grade}\")\n", "    \n", "else:\n", "    print(\"❌ データが生成されていないため、品質チェックを実行できません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.5 年度別血統情報分析"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧬 年度別血統情報の詳細分析\n", "==================================================\n", "❌ 血統情報カラムが見つかりません\n"]}], "source": ["# 年度別血統情報の詳細確認\n", "if not comprehensive_df.empty and 'year' in comprehensive_df.columns:\n", "    print(\"🧬 年度別血統情報の詳細分析\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 血統関連カラムの特定\n", "    pedigree_cols = [col for col in comprehensive_df.columns \n", "                    if any(keyword in col.lower() for keyword in ['father', 'mother'])]\n", "    \n", "    if pedigree_cols:\n", "        print(f\"\\n🎯 血統情報カラム ({len(pedigree_cols)}個):\")\n", "        \n", "        # 年度別血統情報カバー率\n", "        year_pedigree_analysis = {}\n", "        \n", "        for year in sorted(comprehensive_df['year'].unique()):\n", "            year_data = comprehensive_df[comprehensive_df['year'] == year]\n", "            year_analysis = {}\n", "            \n", "            for col in ['father_name', 'mother_name', 'mother_father_name']:\n", "                if col in year_data.columns:\n", "                    coverage = (year_data[col].notna().sum() / len(year_data)) * 100\n", "                    year_analysis[col] = coverage\n", "            \n", "            year_pedigree_analysis[year] = year_analysis\n", "        \n", "        # 年度別カバー率の表示\n", "        print(f\"\\n📊 年度別血統情報カバー率:\")\n", "        print(f\"{'年度':<6} {'父馬':<8} {'母馬':<8} {'母父':<8} {'完全血統':<10}\")\n", "        print(\"-\" * 45)\n", "        \n", "        for year in sorted(year_pedigree_analysis.keys()):\n", "            year_data = comprehensive_df[comprehensive_df['year'] == year]\n", "            analysis = year_pedigree_analysis[year]\n", "            \n", "            father_rate = analysis.get('father_name', 0)\n", "            mother_rate = analysis.get('mother_name', 0)\n", "            mother_father_rate = analysis.get('mother_father_name', 0)\n", "            \n", "            # 完全血統（父・母・母父全て）の割合\n", "            if all(col in year_data.columns for col in ['father_name', 'mother_name', 'mother_father_name']):\n", "                complete_pedigree = year_data[\n", "                    year_data['father_name'].notna() & \n", "                    year_data['mother_name'].notna() & \n", "                    year_data['mother_father_name'].notna()\n", "                ]\n", "                complete_rate = (len(complete_pedigree) / len(year_data)) * 100\n", "            else:\n", "                complete_rate = 0\n", "            \n", "            print(f\"{year:<6} {father_rate:<8.1f} {mother_rate:<8.1f} {mother_father_rate:<8.1f} {complete_rate:<10.1f}\")\n", "        \n", "        # 母父情報の特別分析\n", "        if 'mother_father_name' in comprehensive_df.columns:\n", "            print(f\"\\n🎯 母父情報の詳細分析:\")\n", "            \n", "            total_mother_father = comprehensive_df['mother_father_name'].notna().sum()\n", "            total_records = len(comprehensive_df)\n", "            overall_coverage = (total_mother_father / total_records) * 100\n", "            \n", "            print(f\"   全体母父情報: {total_mother_father:,}件 / {total_records:,}件 ({overall_coverage:.1f}%)\")\n", "            \n", "            # 年度別母父情報の改善傾向\n", "            year_mother_father_rates = []\n", "            for year in sorted(comprehensive_df['year'].unique()):\n", "                year_data = comprehensive_df[comprehensive_df['year'] == year]\n", "                rate = (year_data['mother_father_name'].notna().sum() / len(year_data)) * 100\n", "                year_mother_father_rates.append(rate)\n", "            \n", "            if len(year_mother_father_rates) > 1:\n", "                trend = \"向上\" if year_mother_father_rates[-1] > year_mother_father_rates[0] else \"低下\"\n", "                print(f\"   母父情報の傾向: {trend} ({year_mother_father_rates[0]:.1f}% → {year_mother_father_rates[-1]:.1f}%)\")\n", "    else:\n", "        print(\"❌ 血統情報カラムが見つかりません\")\n", "else:\n", "    print(\"❌ データが生成されていないか、年度情報がありません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.6 データ結合改善提案"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'comprehensive_df' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 97\u001b[0m\n\u001b[0;32m     94\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m suggestions\n\u001b[0;32m     96\u001b[0m \u001b[38;5;66;03m# 改善提案の実行\u001b[39;00m\n\u001b[1;32m---> 97\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[43mcomprehensive_df\u001b[49m\u001b[38;5;241m.\u001b[39mempty \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mquality_report\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mlocals\u001b[39m():\n\u001b[0;32m     98\u001b[0m     improvement_suggestions \u001b[38;5;241m=\u001b[39m suggest_merge_improvements(comprehensive_df, quality_report)\n\u001b[0;32m     99\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[1;31mNameError\u001b[0m: name 'comprehensive_df' is not defined"]}], "source": ["# データ結合の改善提案\n", "def suggest_merge_improvements(df, quality_report):\n", "    \"\"\"\n", "    データ結合の改善提案を生成する関数\n", "    \n", "    Parameters\n", "    ----------\n", "    df : pd.DataFrame\n", "        結合後のデータフレーム\n", "    quality_report : dict\n", "        品質チェック結果\n", "    \n", "    Returns\n", "    -------\n", "    list\n", "        改善提案のリスト\n", "    \"\"\"\n", "    suggestions = []\n", "    \n", "    print(\"💡 データ結合改善提案\")\n", "    print(\"=\" * 40)\n", "    \n", "    # 1. 結合キーの品質改善\n", "    merge_keys = ['race_id', 'horse_id']\n", "    for key in merge_keys:\n", "        if f'{key}_null_rate' in quality_report:\n", "            null_rate = quality_report[f'{key}_null_rate']\n", "            if null_rate > 5:\n", "                suggestion = f\"🔑 {key}の欠損率が{null_rate:.1f}%と高いため、データ収集プロセスの見直しを推奨\"\n", "                suggestions.append(suggestion)\n", "                print(f\"   {suggestion}\")\n", "    \n", "    # 2. 血統情報の改善\n", "    pedigree_cols = ['father_name', 'mother_name', 'mother_father_name']\n", "    missing_pedigree = []\n", "    for col in pedigree_cols:\n", "        if col in df.columns:\n", "            null_rate = (df[col].isnull().sum() / len(df)) * 100\n", "            if null_rate > 50:\n", "                missing_pedigree.append(col)\n", "    \n", "    if missing_pedigree:\n", "        suggestion = f\"🧬 血統情報の充実: {', '.join(missing_pedigree)}の情報収集を強化\"\n", "        suggestions.append(suggestion)\n", "        print(f\"   {suggestion}\")\n", "    \n", "    # 3. データ型の統一\n", "    numeric_cols = ['着順', '人気', '単勝']\n", "    type_issues = []\n", "    for col in numeric_cols:\n", "        if col in df.columns:\n", "            try:\n", "                pd.to_numeric(df[col], errors='raise')\n", "            except:\n", "                type_issues.append(col)\n", "    \n", "    if type_issues:\n", "        suggestion = f\"🔧 データ型統一: {', '.join(type_issues)}の数値型変換処理を改善\"\n", "        suggestions.append(suggestion)\n", "        print(f\"   {suggestion}\")\n", "    \n", "    # 4. パフォーマンス改善\n", "    if quality_report['total_records'] > 100000:\n", "        suggestion = \"⚡ 大量データ処理: インデックス作成やチャンク処理の導入を検討\"\n", "        suggestions.append(suggestion)\n", "        print(f\"   {suggestion}\")\n", "    \n", "    # 5. 年度バランス改善\n", "    if 'year' in df.columns:\n", "        year_distribution = df['year'].value_counts(normalize=True) * 100\n", "        imbalanced_years = year_distribution[(year_distribution < 30) | (year_distribution > 70)]\n", "        if not imbalanced_years.empty:\n", "            suggestion = \"📅 年度バランス調整: データ収集期間の見直しまたは重み付け処理を検討\"\n", "            suggestions.append(suggestion)\n", "            print(f\"   {suggestion}\")\n", "    \n", "    # 6. 新機能提案\n", "    advanced_suggestions = [\n", "        \"🔄 リアルタイム更新: 新しいレースデータの自動取り込み機能\",\n", "        \"🎯 データ検証: 異常値検出とアラート機能の追加\",\n", "        \"📊 統計指標: より詳細な馬・騎手・調教師の成績指標を追加\",\n", "        \"🔍 データ探索: インタラクティブなデータ可視化ツールの導入\",\n", "        \"💾 データ保存: 効率的なデータ形式（Parquet等）での保存機能\"\n", "    ]\n", "    \n", "    print(\"\\n🚀 高度な機能提案:\")\n", "    for suggestion in advanced_suggestions:\n", "        suggestions.append(suggestion)\n", "        print(f\"   {suggestion}\")\n", "    \n", "    if not suggestions:\n", "        print(\"   ✅ 現在のデータ結合品質は良好です\")\n", "    \n", "    return suggestions\n", "\n", "# 改善提案の実行\n", "if not comprehensive_df.empty and 'quality_report' in locals():\n", "    improvement_suggestions = suggest_merge_improvements(comprehensive_df, quality_report)\n", "else:\n", "    print(\"❌ 品質チェックが実行されていないため、改善提案を生成できません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.7 複数年度データのサンプル表示"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 複数年度統合データのサンプル表示\n", "==================================================\n", "\n", "📊 主要カラムのサンプル（各年度から2行ずつ）:\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "year", "rawType": "object", "type": "string"}, {"name": "race_id", "rawType": "object", "type": "string"}, {"name": "horse_id", "rawType": "object", "type": "string"}, {"name": "馬名", "rawType": "object", "type": "string"}, {"name": "着順", "rawType": "object", "type": "unknown"}, {"name": "人気", "rawType": "object", "type": "unknown"}], "ref": "070e967b-3b33-4d5b-a47b-99a8c460ad69", "rows": [["0", "2023", "202301010101", "2021101429", "サトミノキラリ", "1", "1.0"], ["1", "2023", "202301010101", "2021105872", "ベアゴーゴー", "2", "2.0"], ["2", "2024", "202401010101", "2022105244", "ポッドベイダー", "1", "1"], ["3", "2024", "202401010101", "2022106999", "ニシノクードクール", "2", "4"]], "shape": {"columns": 6, "rows": 4}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>year</th>\n", "      <th>race_id</th>\n", "      <th>horse_id</th>\n", "      <th>馬名</th>\n", "      <th>着順</th>\n", "      <th>人気</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023</td>\n", "      <td>202301010101</td>\n", "      <td>2021101429</td>\n", "      <td>サトミノキラリ</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023</td>\n", "      <td>202301010101</td>\n", "      <td>2021105872</td>\n", "      <td>ベアゴーゴー</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024</td>\n", "      <td>202401010101</td>\n", "      <td>2022105244</td>\n", "      <td>ポッドベイダー</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024</td>\n", "      <td>202401010101</td>\n", "      <td>2022106999</td>\n", "      <td>ニシノクードクール</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   year       race_id    horse_id         馬名 着順   人気\n", "0  2023  202301010101  2021101429    サトミノキラリ  1  1.0\n", "1  2023  202301010101  2021105872     ベアゴーゴー  2  2.0\n", "2  2024  202401010101  2022105244    ポッドベイダー  1    1\n", "3  2024  202401010101  2022106999  ニシノクードクール  2    4"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 複数年度データのサンプル表示\n", "if not comprehensive_df.empty:\n", "    print(\"📋 複数年度統合データのサンプル表示\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 重要なカラムを選択して表示\n", "    display_cols = [\n", "        'year', 'race_id', 'horse_id', '馬名', '着順', '人気', 'オッズ',\n", "        'father_name', 'mother_name', 'mother_father_name',\n", "        '天気', '馬場状態', '距離'\n", "    ]\n", "    \n", "    # 利用可能なカラムのみを選択\n", "    available_cols = [col for col in display_cols if col in comprehensive_df.columns]\n", "    \n", "    if available_cols:\n", "        print(f\"\\n📊 主要カラムのサンプル（各年度から2行ずつ）:\")\n", "        \n", "        # 各年度から2行ずつサンプルを表示\n", "        sample_dfs = []\n", "        for year in sorted(comprehensive_df['year'].unique()):\n", "            year_data = comprehensive_df[comprehensive_df['year'] == year]\n", "            year_sample = year_data[available_cols].head(2)\n", "            sample_dfs.append(year_sample)\n", "        \n", "        if sample_dfs:\n", "            combined_sample = pd.concat(sample_dfs, ignore_index=True)\n", "            display(combined_sample)\n", "        \n", "        # 血統情報が完全な馬のサンプル（各年度から1行ずつ）\n", "        if all(col in comprehensive_df.columns for col in ['father_name', 'mother_name', 'mother_father_name']):\n", "            print(f\"\\n🧬 完全血統情報のサンプル（各年度から1行ずつ）:\")\n", "            \n", "            complete_samples = []\n", "            for year in sorted(comprehensive_df['year'].unique()):\n", "                year_data = comprehensive_df[comprehensive_df['year'] == year]\n", "                complete_pedigree_year = year_data[\n", "                    year_data['father_name'].notna() & \n", "                    year_data['mother_name'].notna() & \n", "                    year_data['mother_father_name'].notna()\n", "                ]\n", "                \n", "                if not complete_pedigree_year.empty:\n", "                    sample = complete_pedigree_year[['year', '馬名', 'father_name', 'mother_name', 'mother_father_name']].head(1)\n", "                    complete_samples.append(sample)\n", "            \n", "            if complete_samples:\n", "                complete_sample_df = pd.concat(complete_samples, ignore_index=True)\n", "                display(complete_sample_df)\n", "            else:\n", "                print(\"   完全血統情報のサンプルが見つかりませんでした\")\n", "    else:\n", "        print(\"❌ 表示可能なカラムが見つかりません\")\n", "else:\n", "    print(\"❌ データが生成されていません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 複数年度データの可視化と分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 年度別データ分布の可視化"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 年度別データ分布の可視化\n", "========================================\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 年度別統計サマリー:\n", "   2023年: データ47,672件, レース3,456, 馬11,720, 母父0.0%\n", "   2024年: データ47,181件, レース3,454, 馬11,786, 母父0.0%\n"]}], "source": ["# 年度別データ分布の可視化\n", "if not comprehensive_df.empty and 'year' in comprehensive_df.columns:\n", "    print(\"📊 年度別データ分布の可視化\")\n", "    print(\"=\" * 40)\n", "    \n", "    plt.figure(figsize=(15, 10))\n", "    \n", "    # 年度別レコード数\n", "    plt.subplot(2, 2, 1)\n", "    year_counts = comprehensive_df['year'].value_counts().sort_index()\n", "    year_counts.plot(kind='bar', color='skyblue', edgecolor='black')\n", "    plt.title('年度別データ件数')\n", "    plt.xlabel('年度')\n", "    plt.ylabel('データ件数')\n", "    plt.xticks(rotation=45)\n", "    \n", "    # 年度別血統情報カバー率\n", "    plt.subplot(2, 2, 2)\n", "    if 'mother_father_name' in comprehensive_df.columns:\n", "        year_coverage = []\n", "        years = sorted(comprehensive_df['year'].unique())\n", "        \n", "        for year in years:\n", "            year_data = comprehensive_df[comprehensive_df['year'] == year]\n", "            coverage = (year_data['mother_father_name'].notna().sum() / len(year_data)) * 100\n", "            year_coverage.append(coverage)\n", "        \n", "        plt.plot(years, year_coverage, marker='o', linewidth=2, markersize=8, color='green')\n", "        plt.title('年度別母父情報カバー率')\n", "        plt.xlabel('年度')\n", "        plt.ylabel('カバー率 (%)')\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # 数値をプロット上に表示\n", "        for i, (year, coverage) in enumerate(zip(years, year_coverage)):\n", "            plt.annotate(f'{coverage:.1f}%', (year, coverage), \n", "                        textcoords=\"offset points\", xytext=(0,10), ha='center')\n", "    \n", "    # 年度別ユニークレース数\n", "    plt.subplot(2, 2, 3)\n", "    if 'race_id' in comprehensive_df.columns:\n", "        unique_races_by_year = comprehensive_df.groupby('year')['race_id'].nunique()\n", "        unique_races_by_year.plot(kind='bar', color='orange', edgecolor='black')\n", "        plt.title('年度別ユニークレース数')\n", "        plt.xlabel('年度')\n", "        plt.ylabel('レース数')\n", "        plt.xticks(rotation=45)\n", "    \n", "    # 年度別ユニーク馬数\n", "    plt.subplot(2, 2, 4)\n", "    if 'horse_id' in comprehensive_df.columns:\n", "        unique_horses_by_year = comprehensive_df.groupby('year')['horse_id'].nunique()\n", "        unique_horses_by_year.plot(kind='bar', color='lightcoral', edgecolor='black')\n", "        plt.title('年度別ユニーク馬数')\n", "        plt.xlabel('年度')\n", "        plt.ylabel('馬数')\n", "        plt.xticks(rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 数値サマリー\n", "    print(f\"\\n📈 年度別統計サマリー:\")\n", "    for year in sorted(comprehensive_df['year'].unique()):\n", "        year_data = comprehensive_df[comprehensive_df['year'] == year]\n", "        records = len(year_data)\n", "        races = year_data['race_id'].nunique() if 'race_id' in year_data.columns else 0\n", "        horses = year_data['horse_id'].nunique() if 'horse_id' in year_data.columns else 0\n", "        mother_father_coverage = (year_data['mother_father_name'].notna().sum() / len(year_data)) * 100 if 'mother_father_name' in year_data.columns else 0\n", "        \n", "        print(f\"   {year}年: データ{records:,}件, レース{races:,}, 馬{horses:,}, 母父{mother_father_coverage:.1f}%\")\n", "else:\n", "    print(\"❌ 年度別分析用のデータが不足しています\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 年度間比較分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 年度間比較分析\n", "if not comprehensive_df.empty and 'year' in comprehensive_df.columns and len(comprehensive_df['year'].unique()) > 1:\n", "    print(\"🔍 年度間比較分析\")\n", "    print(\"=\" * 40)\n", "    \n", "    years = sorted(comprehensive_df['year'].unique())\n", "    \n", "    # 年度間での血統情報の改善\n", "    if 'mother_father_name' in comprehensive_df.columns:\n", "        print(f\"\\n🧬 血統情報の年度間比較:\")\n", "        \n", "        pedigree_comparison = {}\n", "        for col in ['father_name', 'mother_name', 'mother_father_name']:\n", "            if col in comprehensive_df.columns:\n", "                year_rates = []\n", "                for year in years:\n", "                    year_data = comprehensive_df[comprehensive_df['year'] == year]\n", "                    rate = (year_data[col].notna().sum() / len(year_data)) * 100\n", "                    year_rates.append(rate)\n", "                pedigree_comparison[col] = year_rates\n", "        \n", "        # 改善度の計算\n", "        for col, rates in pedigree_comparison.items():\n", "            if len(rates) > 1:\n", "                improvement = rates[-1] - rates[0]\n", "                col_name = {'father_name': '父馬', 'mother_name': '母馬', 'mother_father_name': '母父'}[col]\n", "                trend = \"向上\" if improvement > 0 else \"低下\" if improvement < 0 else \"変化なし\"\n", "                print(f\"   {col_name}情報: {rates[0]:.1f}% → {rates[-1]:.1f}% ({improvement:+.1f}%, {trend})\")\n", "    \n", "    # 年度間でのデータ量の変化\n", "    print(f\"\\n📊 データ量の年度間変化:\")\n", "    year_data_counts = []\n", "    for year in years:\n", "        count = len(comprehensive_df[comprehensive_df['year'] == year])\n", "        year_data_counts.append(count)\n", "    \n", "    for i, (year, count) in enumerate(zip(years, year_data_counts)):\n", "        if i > 0:\n", "            change = count - year_data_counts[i-1]\n", "            change_pct = (change / year_data_counts[i-1]) * 100\n", "            trend = \"増加\" if change > 0 else \"減少\" if change < 0 else \"変化なし\"\n", "            print(f\"   {years[i-1]}年 → {year}年: {year_data_counts[i-1]:,} → {count:,} ({change:+,}件, {change_pct:+.1f}%, {trend})\")\n", "        else:\n", "            print(f\"   {year}年: {count:,}件 (基準年)\")\n", "    \n", "    # 年度間での競馬界の変化（レース数、馬数）\n", "    if 'race_id' in comprehensive_df.columns and 'horse_id' in comprehensive_df.columns:\n", "        print(f\"\\n🏇 競馬界の年度間変化:\")\n", "        \n", "        for i, year in enumerate(years):\n", "            year_data = comprehensive_df[comprehensive_df['year'] == year]\n", "            races = year_data['race_id'].nunique()\n", "            horses = year_data['horse_id'].nunique()\n", "            \n", "            if i > 0:\n", "                prev_year_data = comprehensive_df[comprehensive_df['year'] == years[i-1]]\n", "                prev_races = prev_year_data['race_id'].nunique()\n", "                prev_horses = prev_year_data['horse_id'].nunique()\n", "                \n", "                race_change = races - prev_races\n", "                horse_change = horses - prev_horses\n", "                \n", "                print(f\"   {years[i-1]}年 → {year}年:\")\n", "                print(f\"     レース数: {prev_races:,} → {races:,} ({race_change:+,})\")\n", "                print(f\"     馬数: {prev_horses:,} → {horses:,} ({horse_change:+,})\")\n", "            else:\n", "                print(f\"   {year}年: レース{races:,}, 馬{horses:,} (基準年)\")\n", "else:\n", "    print(\"❌ 年度間比較に必要なデータが不足しています\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 複数年度データの保存"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 複数年度データの保存\n", "if not comprehensive_df.empty:\n", "    print(\"💾 複数年度包括的データの保存\")\n", "    print(\"=\" * 50)\n", "    \n", "    # ファイル名に年度範囲を含める\n", "    year_range = f\"{min(TARGET_YEARS)}-{max(TARGET_YEARS)}\" if len(TARGET_YEARS) > 1 else TARGET_YEARS[0]\n", "    filename_prefix = f\"comprehensive_race_data_multi_year_{year_range}\"\n", "    \n", "    # ファイル保存\n", "    pickle_path, csv_path = integrator.save_comprehensive_table(\n", "        filename_prefix=filename_prefix,\n", "        year=None,  # 複数年度なのでNone\n", "        save_pickle=True,\n", "        save_csv=True\n", "    )\n", "    \n", "    print(f\"\\n✅ ファイル保存完了:\")\n", "    if pickle_path:\n", "        print(f\"   📦 Pickle: {pickle_path}\")\n", "        print(f\"      サイズ: {os.path.getsize(pickle_path) / (1024*1024):.1f} MB\")\n", "    \n", "    if csv_path:\n", "        print(f\"   📄 CSV: {csv_path}\")\n", "        print(f\"      サイズ: {os.path.getsize(csv_path) / (1024*1024):.1f} MB\")\n", "    \n", "    # 保存されたデータの概要\n", "    summary = integrator.get_data_summary()\n", "    print(f\"\\n📊 保存データ概要:\")\n", "    print(f\"   対象年度: {', '.join(TARGET_YEARS)} ({len(TARGET_YEARS)}年度)\")\n", "    print(f\"   データ件数: {summary['total_records']:,}件\")\n", "    print(f\"   カラム数: {summary['total_columns']}個\")\n", "    print(f\"   ユニークレース数: {summary['unique_races']:,}\")\n", "    print(f\"   ユニーク馬数: {summary['unique_horses']:,}\")\n", "    \n", "    # 血統情報の充実度\n", "    if 'mother_father_name' in comprehensive_df.columns:\n", "        mother_father_coverage = (comprehensive_df['mother_father_name'].notna().sum() / len(comprehensive_df)) * 100\n", "        print(f\"   母父情報カバー率: {mother_father_coverage:.1f}%\")\n", "    \n", "    # 年度別データ分布\n", "    if 'year' in comprehensive_df.columns:\n", "        print(f\"\\n📅 年度別データ分布:\")\n", "        year_distribution = comprehensive_df['year'].value_counts().sort_index()\n", "        for year, count in year_distribution.items():\n", "            percentage = (count / len(comprehensive_df)) * 100\n", "            print(f\"   {year}年: {count:,}件 ({percentage:.1f}%)\")\n", "    \n", "    print(f\"\\n🎯 このデータは機械学習や統計分析に直接使用できます！\")\n", "    print(f\"複数年度のデータにより、より豊富な特徴量と時系列分析が可能です。\")\n", "else:\n", "    print(\"❌ 保存するデータがありません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. まとめと次のステップ"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# まとめ\n", "print(\"🏁 複数年度包括的競馬データ統合の完了\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n✅ 実装された機能:\")\n", "print(\"   🏇 複数年度対応の包括的データ統合\")\n", "print(\"   📅 年度別処理と統合\")\n", "print(\"   🏁 レース情報の統合（天気、馬場状態、距離等）\")\n", "print(\"   🐎 馬基本情報の統合（血統、調教師、馬主等）\")\n", "print(\"   🎯 母父情報の追加（重要な血統要素）\")\n", "print(\"   📈 馬過去成績統計の統合（直近N戦の統計）\")\n", "print(\"   📊 年度別分析と比較機能\")\n", "print(\"   💾 複数形式での保存（CSV、Pickle）\")\n", "print(\"   ⚙️ 柔軟な設定オプション\")\n", "print(\"   🚀 並列処理による高速化\")\n", "\n", "if not comprehensive_df.empty:\n", "    print(f\"\\n📊 生成されたデータの概要:\")\n", "    print(f\"   対象年度: {', '.join(TARGET_YEARS)} ({len(TARGET_YEARS)}年度)\")\n", "    print(f\"   データ件数: {len(comprehensive_df):,}件\")\n", "    print(f\"   カラム数: {len(comprehensive_df.columns)}個\")\n", "    print(f\"   ユニークレース数: {comprehensive_df['race_id'].nunique() if 'race_id' in comprehensive_df.columns else 0:,}\")\n", "    print(f\"   ユニーク馬数: {comprehensive_df['horse_id'].nunique() if 'horse_id' in comprehensive_df.columns else 0:,}\")\n", "    \n", "    # 血統情報の最終確認\n", "    if 'mother_father_name' in comprehensive_df.columns:\n", "        mother_father_final = comprehensive_df['mother_father_name'].notna().sum()\n", "        mother_father_final_rate = (mother_father_final / len(comprehensive_df)) * 100\n", "        print(f\"   🎯 母父情報: {mother_father_final:,}件 ({mother_father_final_rate:.1f}%)\")\n", "    \n", "    # 処理時間の総計\n", "    if year_summaries:\n", "        total_processing_time = sum(summary['processing_time'] for summary in year_summaries.values())\n", "        print(f\"   ⏱️ 総処理時間: {total_processing_time:.1f}秒 ({total_processing_time/60:.1f}分)\")\n", "\n", "print(f\"\\n🚀 複数年度データの活用メリット:\")\n", "print(\"   1. 時系列分析による競馬界の変化の把握\")\n", "print(\"   2. 年度をまたいだ馬の成長パターン分析\")\n", "print(\"   3. 血統情報の年度別改善傾向の確認\")\n", "print(\"   4. より豊富なデータによる予測精度向上\")\n", "print(\"   5. 長期的なトレンド分析\")\n", "\n", "print(f\"\\n🎯 次のステップ:\")\n", "print(\"   1. 時系列機械学習モデルの構築\")\n", "print(\"   2. 年度別血統と成績の相関分析\")\n", "print(\"   3. 母父の影響度の年度別変化分析\")\n", "print(\"   4. 複数年度データを活用した予測モデル\")\n", "print(\"   5. 年度間比較ダッシュボードの作成\")\n", "\n", "print(f\"\\n💡 複数年度データ活用のヒント:\")\n", "print(\"   - 年度カラムを使って時系列分析を行う\")\n", "print(\"   - 血統情報の年度別変化を特徴量として活用\")\n", "print(\"   - 母父情報は年度が新しいほど充実している\")\n", "print(\"   - 過去成績統計は馬の成長を表す重要な指標\")\n", "print(\"   - レース条件の年度別変化も分析対象\")\n", "\n", "print(f\"\\n🎉 母父情報を含む複数年度包括的競馬データ統合が完了しました！\")\n", "print(f\"このデータを使って、より高度で精度の高い競馬予測モデルを構築してください。\")\n", "print(f\"複数年度のデータにより、時系列分析や長期トレンド分析も可能になります。\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 4}